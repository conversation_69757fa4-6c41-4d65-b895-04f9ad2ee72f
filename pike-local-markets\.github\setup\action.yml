name: Setup Node and Foundry
description: Comprehensive setup for Node.js environment with Foundry toolchain

runs:
  using: composite
  steps:
    - name: Install Foundry
      uses: foundry-rs/foundry-toolchain@v1
    - uses: actions/setup-node@v4
      with:
        node-version: 20.x
        check-latest: true
        cache: yarn

    - name: Install dependencies
      shell: bash
      run: yarn install --frozen-lockfile --audit
