{"address": "0x7ad1Fe8a6B0927d34c8262D3549DC1e6E788F1A2", "abi": [{"type": "constructor", "inputs": [{"name": "implementation", "type": "address", "internalType": "address"}, {"name": "_data", "type": "bytes", "internalType": "bytes"}], "stateMutability": "payable"}, {"type": "fallback", "stateMutability": "payable"}, {"type": "event", "name": "Upgraded", "inputs": [{"name": "implementation", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "AddressEmptyCode", "inputs": [{"name": "target", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC1967InvalidImplementation", "inputs": [{"name": "implementation", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC1967Non<PERSON>ayable", "inputs": []}, {"type": "error", "name": "FailedInnerCall", "inputs": []}], "constructorArgs": ["0xa4268d6bcbd4DB3301AD728879D54F0dd9651ea9", "0x"], "linkedLibraries": {}, "deployTxnHash": "0xa4bd5851ad130d099746a6e91dc153c2d43d46e6889c6bfcf83fb0198dac0bb1", "deployTxnBlockNumber": "28358302", "deployTimestamp": "1743505951", "sourceName": "node_modules/@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol", "contractName": "ERC1967Proxy", "deployedOn": "deploy.InitialProxy", "gasUsed": 132258, "gasCost": "2380096"}