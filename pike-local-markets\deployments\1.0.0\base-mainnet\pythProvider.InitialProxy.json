{"address": "0x324B7e2620B866aaC6E7Ef46E36334698D2d0b7D", "abi": [{"type": "constructor", "inputs": [{"name": "implementation", "type": "address", "internalType": "address"}, {"name": "_data", "type": "bytes", "internalType": "bytes"}], "stateMutability": "payable"}, {"type": "fallback", "stateMutability": "payable"}, {"type": "event", "name": "Upgraded", "inputs": [{"name": "implementation", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "AddressEmptyCode", "inputs": [{"name": "target", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC1967InvalidImplementation", "inputs": [{"name": "implementation", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC1967Non<PERSON>ayable", "inputs": []}, {"type": "error", "name": "FailedInnerCall", "inputs": []}], "constructorArgs": ["0xF9d55142257DC6ffB42bf6ed5a97a3E7334a21a6", "0x"], "linkedLibraries": {}, "deployTxnHash": "0xd07187295316c368460927b24d71d672b4ad747aeaa1fe79f3e4cf75c5f73db9", "deployTxnBlockNumber": "28358312", "deployTimestamp": "1743505971", "sourceName": "node_modules/@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol", "contractName": "ERC1967Proxy", "deployedOn": "deploy.InitialProxy", "gasUsed": 132270, "gasCost": "2385407"}