{"address": "0x23e5B28Ca1c4515B98baD4DB53F796f688334A4e", "abi": [{"type": "constructor", "inputs": [{"name": "implementation_", "type": "address", "internalType": "address"}, {"name": "initialOwner", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "implementation", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "upgradeTo", "inputs": [{"name": "newImplementation", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Upgraded", "inputs": [{"name": "implementation", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "BeaconInvalidImplementation", "inputs": [{"name": "implementation", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}], "constructorArgs": ["0xaBF4b70C67cf74Bd161E178CF1Fa0bb1EB5daC94", "0x92Bc7D2305EC97E138a5c98F6f5FD69703fe0589"], "linkedLibraries": {}, "deployTxnHash": "0x51c860dbe45a5083272bf4a6b63e42a0851bdf27e4c74c0d47abb1cf701db845", "deployTxnBlockNumber": "1092861", "deployTimestamp": "**********", "sourceName": "node_modules/@openzeppelin/contracts/proxy/beacon/UpgradeableBeacon.sol", "contractName": "UpgradeableBeacon", "deployedOn": "contract.oracleEngineBeacon", "gasUsed": 305889, "gasCost": "**********"}