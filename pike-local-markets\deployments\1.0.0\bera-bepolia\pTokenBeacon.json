{"address": "0xdF2746aec0e2271b8d659795857A68D2865B5967", "abi": [{"type": "constructor", "inputs": [{"name": "implementation_", "type": "address", "internalType": "address"}, {"name": "initialOwner", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "implementation", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "upgradeTo", "inputs": [{"name": "newImplementation", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Upgraded", "inputs": [{"name": "implementation", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "BeaconInvalidImplementation", "inputs": [{"name": "implementation", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}], "constructorArgs": ["0xf3e314aaa145ac45f92d0bf29b07c6eb4ace0993", "0x92Bc7D2305EC97E138a5c98F6f5FD69703fe0589"], "linkedLibraries": {}, "deployTxnHash": "0xdda6134e4dfb54d88c50a6beae7bc1db6926bb8acccd6cbf33a18c3de91be0a6", "deployTxnBlockNumber": "1092888", "deployTimestamp": "**********", "sourceName": "node_modules/@openzeppelin/contracts/proxy/beacon/UpgradeableBeacon.sol", "contractName": "UpgradeableBeacon", "deployedOn": "contract.pTokenBeacon", "gasUsed": 305889, "gasCost": "**********"}