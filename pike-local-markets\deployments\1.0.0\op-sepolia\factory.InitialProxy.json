{"address": "0xb0151C27AA9f98C4dDDA8b7A784Cdb2c5d4d676d", "abi": [{"type": "constructor", "inputs": [{"name": "implementation", "type": "address", "internalType": "address"}, {"name": "_data", "type": "bytes", "internalType": "bytes"}], "stateMutability": "payable"}, {"type": "fallback", "stateMutability": "payable"}, {"type": "event", "name": "Upgraded", "inputs": [{"name": "implementation", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "AddressEmptyCode", "inputs": [{"name": "target", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC1967InvalidImplementation", "inputs": [{"name": "implementation", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC1967Non<PERSON>ayable", "inputs": []}, {"type": "error", "name": "FailedInnerCall", "inputs": []}], "constructorArgs": ["0x5831Ace9b7F99c5729893bDA304104d4A49E7A1F", "0x"], "linkedLibraries": {}, "deployTxnHash": "0x5f66433deba3626155998f5d675f0ad9ab9535feb327f7d95580fed4b044aaae", "deployTxnBlockNumber": "26126625", "deployTimestamp": "1744055790", "sourceName": "node_modules/@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol", "contractName": "ERC1967Proxy", "deployedOn": "deploy.InitialProxy", "gasUsed": 132270, "gasCost": "1001837"}