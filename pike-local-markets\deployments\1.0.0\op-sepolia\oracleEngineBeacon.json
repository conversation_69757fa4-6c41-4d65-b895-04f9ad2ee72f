{"address": "0x388ddEEB518067a32c200D55194Df16C4203f2a6", "abi": [{"type": "constructor", "inputs": [{"name": "implementation_", "type": "address", "internalType": "address"}, {"name": "initialOwner", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "implementation", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "upgradeTo", "inputs": [{"name": "newImplementation", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Upgraded", "inputs": [{"name": "implementation", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "BeaconInvalidImplementation", "inputs": [{"name": "implementation", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}], "constructorArgs": ["0xFe5482d06779c6b67EedB8A98dcF0E7B33B5b17D", "0x92Bc7D2305EC97E138a5c98F6f5FD69703fe0589"], "linkedLibraries": {}, "deployTxnHash": "0x0a37389aa6ed546267528e3a9be831f23f32204cec79d6eb1929f3755b1a7b50", "deployTxnBlockNumber": "********", "deployTimestamp": "**********", "sourceName": "node_modules/@openzeppelin/contracts/proxy/beacon/UpgradeableBeacon.sol", "contractName": "UpgradeableBeacon", "deployedOn": "contract.oracleEngineBeacon", "gasUsed": 305889, "gasCost": "1001816"}