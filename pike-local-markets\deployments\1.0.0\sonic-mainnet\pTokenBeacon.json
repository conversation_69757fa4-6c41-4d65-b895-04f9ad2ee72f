{"address": "0xB26eee8606Ec913b8e14A473486524a48a8F6D87", "abi": [{"type": "constructor", "inputs": [{"name": "implementation_", "type": "address", "internalType": "address"}, {"name": "initialOwner", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "implementation", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "upgradeTo", "inputs": [{"name": "newImplementation", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Upgraded", "inputs": [{"name": "implementation", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "BeaconInvalidImplementation", "inputs": [{"name": "implementation", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}], "constructorArgs": ["0xf9d55142257dc6ffb42bf6ed5a97a3e7334a21a6", "0xE80bbcAB9E20fc193Ef768B68d69F363a7f9a2a1"], "linkedLibraries": {}, "deployTxnHash": "0xf88d4bde133b462b8e58c7641eefefcdf4ac3a174b0952cc1efba2bdf248190d", "deployTxnBlockNumber": "********", "deployTimestamp": "**********", "sourceName": "node_modules/@openzeppelin/contracts/proxy/beacon/UpgradeableBeacon.sol", "contractName": "UpgradeableBeacon", "deployedOn": "contract.pTokenBeacon", "gasUsed": 305889, "gasCost": "***********"}