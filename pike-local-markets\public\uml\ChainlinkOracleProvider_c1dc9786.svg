<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 5.0.0 (20220707.1540)
 -->
<!-- Title: UmlClassDiagram Pages: 1 -->
<svg width="3931pt" height="1697pt"
 viewBox="0.00 0.00 3931.23 1697.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 1693)">
<title>UmlClassDiagram</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-1693 3927.23,-1693 3927.23,4 -4,4"/>
<!-- 0 -->
<g id="node1" class="node">
<title>0</title>
<polygon fill="#f2f2f2" stroke="black" points="147.94,-1313.1 147.94,-1480.3 936.15,-1480.3 936.15,-1313.1 147.94,-1313.1"/>
<text text-anchor="middle" x="542.05" y="-1463.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="542.05" y="-1446.9" font-family="Times,serif" font-size="14.00">AggregatorV3Interface</text>
<text text-anchor="middle" x="542.05" y="-1430.1" font-family="Times,serif" font-size="14.00">public/flatten/ChainlinkOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="147.94,-1421.9 936.15,-1421.9 "/>
<text text-anchor="start" x="155.94" y="-1405.3" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="155.94" y="-1388.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;decimals(): uint8</text>
<text text-anchor="start" x="155.94" y="-1371.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;description(): string</text>
<text text-anchor="start" x="155.94" y="-1354.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;version(): uint256</text>
<text text-anchor="start" x="155.94" y="-1338.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getRoundData(_roundId: uint80): (roundId: uint80, answer: int256, startedAt: uint256, updatedAt: uint256, answeredInRound: uint80)</text>
<text text-anchor="start" x="155.94" y="-1321.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;latestRoundData(): (roundId: uint80, answer: int256, startedAt: uint256, updatedAt: uint256, answeredInRound: uint80)</text>
</g>
<!-- 1 -->
<g id="node2" class="node">
<title>1</title>
<polygon fill="#f2f2f2" stroke="black" points="2044.86,-949.7 2044.86,-1049.7 2339.23,-1049.7 2339.23,-949.7 2044.86,-949.7"/>
<text text-anchor="middle" x="2192.05" y="-1033.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="2192.05" y="-1016.3" font-family="Times,serif" font-size="14.00">IERC1822Proxiable</text>
<text text-anchor="middle" x="2192.05" y="-999.5" font-family="Times,serif" font-size="14.00">public/flatten/ChainlinkOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="2044.86,-991.3 2339.23,-991.3 "/>
<text text-anchor="start" x="2052.86" y="-974.7" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="2052.86" y="-957.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;proxiableUUID(): bytes32</text>
</g>
<!-- 2 -->
<g id="node3" class="node">
<title>2</title>
<polygon fill="#f2f2f2" stroke="black" points="2018.86,-1346.7 2018.86,-1446.7 2313.23,-1446.7 2313.23,-1346.7 2018.86,-1346.7"/>
<text text-anchor="middle" x="2166.05" y="-1430.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="2166.05" y="-1413.3" font-family="Times,serif" font-size="14.00">IBeacon</text>
<text text-anchor="middle" x="2166.05" y="-1396.5" font-family="Times,serif" font-size="14.00">public/flatten/ChainlinkOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="2018.86,-1388.3 2313.23,-1388.3 "/>
<text text-anchor="start" x="2026.86" y="-1371.7" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="2026.86" y="-1354.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;implementation(): address</text>
</g>
<!-- 3 -->
<g id="node4" class="node">
<title>3</title>
<polygon fill="#f2f2f2" stroke="black" points="882.37,-882.5 882.37,-1116.9 1311.73,-1116.9 1311.73,-882.5 882.37,-882.5"/>
<text text-anchor="middle" x="1097.05" y="-1100.3" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="1097.05" y="-1083.5" font-family="Times,serif" font-size="14.00">IERC20</text>
<text text-anchor="middle" x="1097.05" y="-1066.7" font-family="Times,serif" font-size="14.00">public/flatten/ChainlinkOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="882.37,-1058.5 1311.73,-1058.5 "/>
<text text-anchor="start" x="890.37" y="-1041.9" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="890.37" y="-1025.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;totalSupply(): uint256</text>
<text text-anchor="start" x="890.37" y="-1008.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;balanceOf(account: address): uint256</text>
<text text-anchor="start" x="890.37" y="-991.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;transfer(to: address, value: uint256): bool</text>
<text text-anchor="start" x="890.37" y="-974.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;allowance(owner: address, spender: address): uint256</text>
<text text-anchor="start" x="890.37" y="-957.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;approve(spender: address, value: uint256): bool</text>
<text text-anchor="start" x="890.37" y="-941.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;transferFrom(from: address, to: address, value: uint256): bool</text>
<text text-anchor="start" x="890.37" y="-924.3" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="890.37" y="-907.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; Transfer(from: address, to: address, value: uint256)</text>
<text text-anchor="start" x="890.37" y="-890.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; Approval(owner: address, spender: address, value: uint256)</text>
</g>
<!-- 4 -->
<g id="node5" class="node">
<title>4</title>
<polygon fill="#f2f2f2" stroke="black" points="2331.61,-1279.5 2331.61,-1513.9 2830.48,-1513.9 2830.48,-1279.5 2331.61,-1279.5"/>
<text text-anchor="middle" x="2581.05" y="-1497.3" font-family="Times,serif" font-size="14.00">&lt;&lt;Library&gt;&gt;</text>
<text text-anchor="middle" x="2581.05" y="-1480.5" font-family="Times,serif" font-size="14.00">Address</text>
<text text-anchor="middle" x="2581.05" y="-1463.7" font-family="Times,serif" font-size="14.00">public/flatten/ChainlinkOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="2331.61,-1455.5 2830.48,-1455.5 "/>
<text text-anchor="start" x="2339.61" y="-1438.9" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="2339.61" y="-1422.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_revert(returndata: bytes)</text>
<text text-anchor="start" x="2339.61" y="-1405.3" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="2339.61" y="-1388.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;sendValue(recipient: address, amount: uint256)</text>
<text text-anchor="start" x="2339.61" y="-1371.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;functionCall(target: address, data: bytes): bytes</text>
<text text-anchor="start" x="2339.61" y="-1354.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;functionCallWithValue(target: address, data: bytes, value: uint256): bytes</text>
<text text-anchor="start" x="2339.61" y="-1338.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;functionStaticCall(target: address, data: bytes): bytes</text>
<text text-anchor="start" x="2339.61" y="-1321.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;functionDelegateCall(target: address, data: bytes): bytes</text>
<text text-anchor="start" x="2339.61" y="-1304.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;verifyCallResultFromTarget(target: address, success: bool, returndata: bytes): bytes</text>
<text text-anchor="start" x="2339.61" y="-1287.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;verifyCallResult(success: bool, returndata: bytes): bytes</text>
</g>
<!-- 5 -->
<g id="node6" class="node">
<title>5</title>
<polygon fill="#f2f2f2" stroke="black" points="2848.65,-1287.9 2848.65,-1505.5 3143.45,-1505.5 3143.45,-1287.9 2848.65,-1287.9"/>
<text text-anchor="middle" x="2996.05" y="-1488.9" font-family="Times,serif" font-size="14.00">&lt;&lt;Library&gt;&gt;</text>
<text text-anchor="middle" x="2996.05" y="-1472.1" font-family="Times,serif" font-size="14.00">StorageSlot</text>
<text text-anchor="middle" x="2996.05" y="-1455.3" font-family="Times,serif" font-size="14.00">public/flatten/ChainlinkOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="2848.65,-1447.1 3143.45,-1447.1 "/>
<text text-anchor="start" x="2856.65" y="-1430.5" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="2856.65" y="-1413.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getAddressSlot(slot: bytes32): (r: AddressSlot)</text>
<text text-anchor="start" x="2856.65" y="-1396.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getBooleanSlot(slot: bytes32): (r: BooleanSlot)</text>
<text text-anchor="start" x="2856.65" y="-1380.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getBytes32Slot(slot: bytes32): (r: Bytes32Slot)</text>
<text text-anchor="start" x="2856.65" y="-1363.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getUint256Slot(slot: bytes32): (r: Uint256Slot)</text>
<text text-anchor="start" x="2856.65" y="-1346.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getStringSlot(slot: bytes32): (r: StringSlot)</text>
<text text-anchor="start" x="2856.65" y="-1329.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getStringSlot(store: string): (r: StringSlot)</text>
<text text-anchor="start" x="2856.65" y="-1312.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getBytesSlot(slot: bytes32): (r: BytesSlot)</text>
<text text-anchor="start" x="2856.65" y="-1296.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getBytesSlot(store: bytes): (r: BytesSlot)</text>
</g>
<!-- 6 -->
<g id="node7" class="node">
<title>6</title>
<polygon fill="#f2f2f2" stroke="black" points="2068.86,-1596.9 2068.86,-1680.1 2363.23,-1680.1 2363.23,-1596.9 2068.86,-1596.9"/>
<text text-anchor="middle" x="2216.05" y="-1663.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="2216.05" y="-1646.7" font-family="Times,serif" font-size="14.00">AddressSlot</text>
<text text-anchor="middle" x="2216.05" y="-1629.9" font-family="Times,serif" font-size="14.00">public/flatten/ChainlinkOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="2068.86,-1621.7 2363.23,-1621.7 "/>
<text text-anchor="start" x="2076.86" y="-1605.1" font-family="Times,serif" font-size="14.00">value: address</text>
</g>
<!-- 5&#45;&gt;6 -->
<g id="edge7" class="edge">
<title>5&#45;&gt;6</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2898.03,-1505.77C2877.5,-1524.13 2854.57,-1540.85 2830.05,-1552 2735.3,-1595.09 2465.22,-1568.13 2363.05,-1588 2353.94,-1589.77 2344.62,-1591.9 2335.3,-1594.26"/>
<polygon fill="black" stroke="black" points="2334.39,-1590.88 2325.6,-1596.79 2336.16,-1597.65 2334.39,-1590.88"/>
</g>
<!-- 7 -->
<g id="node8" class="node">
<title>7</title>
<polygon fill="#f2f2f2" stroke="black" points="2380.86,-1596.9 2380.86,-1680.1 2675.23,-1680.1 2675.23,-1596.9 2380.86,-1596.9"/>
<text text-anchor="middle" x="2528.05" y="-1663.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="2528.05" y="-1646.7" font-family="Times,serif" font-size="14.00">BooleanSlot</text>
<text text-anchor="middle" x="2528.05" y="-1629.9" font-family="Times,serif" font-size="14.00">public/flatten/ChainlinkOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="2380.86,-1621.7 2675.23,-1621.7 "/>
<text text-anchor="start" x="2388.86" y="-1605.1" font-family="Times,serif" font-size="14.00">value: bool</text>
</g>
<!-- 5&#45;&gt;7 -->
<g id="edge8" class="edge">
<title>5&#45;&gt;7</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2893.54,-1505.66C2873.94,-1523.22 2852.49,-1539.67 2830.05,-1552 2768.07,-1586.06 2743.25,-1569.29 2675.05,-1588 2668.17,-1589.89 2661.14,-1591.88 2654.07,-1593.94"/>
<polygon fill="black" stroke="black" points="2653.01,-1590.6 2644.4,-1596.78 2654.99,-1597.32 2653.01,-1590.6"/>
</g>
<!-- 8 -->
<g id="node9" class="node">
<title>8</title>
<polygon fill="#f2f2f2" stroke="black" points="2692.86,-1596.9 2692.86,-1680.1 2987.23,-1680.1 2987.23,-1596.9 2692.86,-1596.9"/>
<text text-anchor="middle" x="2840.05" y="-1663.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="2840.05" y="-1646.7" font-family="Times,serif" font-size="14.00">Bytes32Slot</text>
<text text-anchor="middle" x="2840.05" y="-1629.9" font-family="Times,serif" font-size="14.00">public/flatten/ChainlinkOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="2692.86,-1621.7 2987.23,-1621.7 "/>
<text text-anchor="start" x="2700.86" y="-1605.1" font-family="Times,serif" font-size="14.00">value: bytes32</text>
</g>
<!-- 5&#45;&gt;8 -->
<g id="edge9" class="edge">
<title>5&#45;&gt;8</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2919.08,-1505.67C2900.3,-1534.33 2881.43,-1563.91 2866.8,-1587.77"/>
<polygon fill="black" stroke="black" points="2863.63,-1586.26 2861.41,-1596.62 2869.6,-1589.9 2863.63,-1586.26"/>
</g>
<!-- 9 -->
<g id="node10" class="node">
<title>9</title>
<polygon fill="#f2f2f2" stroke="black" points="3004.86,-1596.9 3004.86,-1680.1 3299.23,-1680.1 3299.23,-1596.9 3004.86,-1596.9"/>
<text text-anchor="middle" x="3152.05" y="-1663.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="3152.05" y="-1646.7" font-family="Times,serif" font-size="14.00">Uint256Slot</text>
<text text-anchor="middle" x="3152.05" y="-1629.9" font-family="Times,serif" font-size="14.00">public/flatten/ChainlinkOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="3004.86,-1621.7 3299.23,-1621.7 "/>
<text text-anchor="start" x="3012.86" y="-1605.1" font-family="Times,serif" font-size="14.00">value: uint256</text>
</g>
<!-- 5&#45;&gt;9 -->
<g id="edge10" class="edge">
<title>5&#45;&gt;9</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M3059.57,-1505.67C3078.15,-1534.46 3097.86,-1564.17 3114.32,-1588.09"/>
<polygon fill="black" stroke="black" points="3111.66,-1590.39 3120.23,-1596.62 3117.41,-1586.41 3111.66,-1590.39"/>
</g>
<!-- 10 -->
<g id="node11" class="node">
<title>10</title>
<polygon fill="#f2f2f2" stroke="black" points="3316.86,-1596.9 3316.86,-1680.1 3611.23,-1680.1 3611.23,-1596.9 3316.86,-1596.9"/>
<text text-anchor="middle" x="3464.05" y="-1663.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="3464.05" y="-1646.7" font-family="Times,serif" font-size="14.00">StringSlot</text>
<text text-anchor="middle" x="3464.05" y="-1629.9" font-family="Times,serif" font-size="14.00">public/flatten/ChainlinkOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="3316.86,-1621.7 3611.23,-1621.7 "/>
<text text-anchor="start" x="3324.86" y="-1605.1" font-family="Times,serif" font-size="14.00">value: string</text>
</g>
<!-- 5&#45;&gt;10 -->
<g id="edge11" class="edge">
<title>5&#45;&gt;10</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M3143.55,-1476.55C3217.78,-1514.95 3305.5,-1559.73 3370.41,-1592.3"/>
<polygon fill="black" stroke="black" points="3368.9,-1595.46 3379.41,-1596.81 3372.04,-1589.2 3368.9,-1595.46"/>
</g>
<!-- 11 -->
<g id="node12" class="node">
<title>11</title>
<polygon fill="#f2f2f2" stroke="black" points="3628.86,-1596.9 3628.86,-1680.1 3923.23,-1680.1 3923.23,-1596.9 3628.86,-1596.9"/>
<text text-anchor="middle" x="3776.05" y="-1663.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="3776.05" y="-1646.7" font-family="Times,serif" font-size="14.00">BytesSlot</text>
<text text-anchor="middle" x="3776.05" y="-1629.9" font-family="Times,serif" font-size="14.00">public/flatten/ChainlinkOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="3628.86,-1621.7 3923.23,-1621.7 "/>
<text text-anchor="start" x="3636.86" y="-1605.1" font-family="Times,serif" font-size="14.00">value: bytes</text>
</g>
<!-- 5&#45;&gt;11 -->
<g id="edge12" class="edge">
<title>5&#45;&gt;11</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M3143.75,-1444.43C3268.37,-1482.66 3451.4,-1538.33 3611.05,-1588 3617.1,-1589.88 3623.28,-1591.81 3629.53,-1593.77"/>
<polygon fill="black" stroke="black" points="3628.81,-1597.21 3639.4,-1596.86 3630.91,-1590.53 3628.81,-1597.21"/>
</g>
<!-- 6&#45;&gt;5 -->
<g id="edge1" class="edge">
<title>6&#45;&gt;5</title>
<path fill="none" stroke="black" d="M2343.51,-1596.79C2356.11,-1593.42 2368.77,-1590.39 2381.05,-1588 2483.22,-1568.13 2753.3,-1595.09 2848.05,-1552 2868.93,-1542.5 2888.66,-1528.97 2906.5,-1513.83"/>
<polygon fill="black" stroke="black" points="2906.64,-1513.7 2908.51,-1506.73 2915.65,-1505.77 2913.79,-1512.74 2906.64,-1513.7"/>
</g>
<!-- 7&#45;&gt;5 -->
<g id="edge2" class="edge">
<title>7&#45;&gt;5</title>
<path fill="none" stroke="black" d="M2662.35,-1596.78C2672.72,-1593.71 2683.05,-1590.74 2693.05,-1588 2761.25,-1569.29 2786.07,-1586.06 2848.05,-1552 2866.98,-1541.6 2885.21,-1528.26 2902.02,-1513.8"/>
<polygon fill="black" stroke="black" points="2902.23,-1513.62 2904.07,-1506.64 2911.21,-1505.66 2909.37,-1512.63 2902.23,-1513.62"/>
</g>
<!-- 8&#45;&gt;5 -->
<g id="edge3" class="edge">
<title>8&#45;&gt;5</title>
<path fill="none" stroke="black" d="M2871.87,-1596.62C2887.53,-1574.14 2907.01,-1544.97 2925.88,-1515.93"/>
<polygon fill="black" stroke="black" points="2926,-1515.75 2925.91,-1508.54 2932.53,-1505.67 2932.62,-1512.88 2926,-1515.75"/>
</g>
<!-- 9&#45;&gt;5 -->
<g id="edge4" class="edge">
<title>9&#45;&gt;5</title>
<path fill="none" stroke="black" d="M3130.68,-1596.62C3117.09,-1574.14 3098.62,-1544.97 3079.71,-1515.93"/>
<polygon fill="black" stroke="black" points="3079.57,-1515.72 3072.94,-1512.89 3073.01,-1505.67 3079.64,-1508.51 3079.57,-1515.72"/>
</g>
<!-- 10&#45;&gt;5 -->
<g id="edge5" class="edge">
<title>10&#45;&gt;5</title>
<path fill="none" stroke="black" d="M3389.83,-1596.8C3326.84,-1563.74 3234.34,-1516.15 3154.8,-1475.78"/>
<polygon fill="black" stroke="black" points="3154.37,-1475.56 3147.21,-1476.42 3143.67,-1470.14 3150.83,-1469.28 3154.37,-1475.56"/>
</g>
<!-- 11&#45;&gt;5 -->
<g id="edge6" class="edge">
<title>11&#45;&gt;5</title>
<path fill="none" stroke="black" d="M3657.36,-1596.86C3647.8,-1593.85 3638.27,-1590.87 3629.05,-1588 3468.2,-1537.96 3283.63,-1481.83 3155.42,-1443.58"/>
<polygon fill="black" stroke="black" points="3155.08,-1443.47 3148.19,-1445.59 3143.58,-1440.05 3150.47,-1437.93 3155.08,-1443.47"/>
</g>
<!-- 12 -->
<g id="node13" class="node">
<title>12</title>
<polygon fill="#f2f2f2" stroke="black" points="1510.16,-1241.9 1510.16,-1551.5 1823.93,-1551.5 1823.93,-1241.9 1510.16,-1241.9"/>
<text text-anchor="middle" x="1667.05" y="-1534.9" font-family="Times,serif" font-size="14.00">&lt;&lt;Abstract&gt;&gt;</text>
<text text-anchor="middle" x="1667.05" y="-1518.1" font-family="Times,serif" font-size="14.00">Initializable</text>
<text text-anchor="middle" x="1667.05" y="-1501.3" font-family="Times,serif" font-size="14.00">public/flatten/ChainlinkOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="1510.16,-1493.1 1823.93,-1493.1 "/>
<text text-anchor="start" x="1518.16" y="-1476.5" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="1518.16" y="-1459.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;INITIALIZABLE_STORAGE: bytes32</text>
<polyline fill="none" stroke="black" points="1510.16,-1451.5 1823.93,-1451.5 "/>
<text text-anchor="start" x="1518.16" y="-1434.9" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="1518.16" y="-1418.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_getInitializableStorage(): ($: InitializableStorage)</text>
<text text-anchor="start" x="1518.16" y="-1401.3" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="1518.16" y="-1384.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_checkInitializing()</text>
<text text-anchor="start" x="1518.16" y="-1367.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_disableInitializers()</text>
<text text-anchor="start" x="1518.16" y="-1350.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_getInitializedVersion(): uint64</text>
<text text-anchor="start" x="1518.16" y="-1334.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_isInitializing(): bool</text>
<text text-anchor="start" x="1518.16" y="-1317.3" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="1518.16" y="-1300.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; Initialized(version: uint64)</text>
<text text-anchor="start" x="1518.16" y="-1283.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;modifier&gt;&gt; initializer()</text>
<text text-anchor="start" x="1518.16" y="-1266.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;modifier&gt;&gt; reinitializer(version: uint64)</text>
<text text-anchor="start" x="1518.16" y="-1250.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;modifier&gt;&gt; onlyInitializing()</text>
</g>
<!-- 13 -->
<g id="node14" class="node">
<title>13</title>
<polygon fill="#f2f2f2" stroke="black" points="1519.86,-1588.5 1519.86,-1688.5 1814.23,-1688.5 1814.23,-1588.5 1519.86,-1588.5"/>
<text text-anchor="middle" x="1667.05" y="-1671.9" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="1667.05" y="-1655.1" font-family="Times,serif" font-size="14.00">InitializableStorage</text>
<text text-anchor="middle" x="1667.05" y="-1638.3" font-family="Times,serif" font-size="14.00">public/flatten/ChainlinkOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="1519.86,-1630.1 1814.23,-1630.1 "/>
<text text-anchor="start" x="1527.86" y="-1613.5" font-family="Times,serif" font-size="14.00">_initialized: uint64</text>
<text text-anchor="start" x="1527.86" y="-1596.7" font-family="Times,serif" font-size="14.00">_initializing: bool</text>
</g>
<!-- 12&#45;&gt;13 -->
<g id="edge14" class="edge">
<title>12&#45;&gt;13</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1660.54,-1551.6C1660.68,-1560.71 1660.87,-1569.53 1661.12,-1577.83"/>
<polygon fill="black" stroke="black" points="1657.63,-1578.18 1661.46,-1588.05 1664.62,-1577.94 1657.63,-1578.18"/>
</g>
<!-- 13&#45;&gt;12 -->
<g id="edge13" class="edge">
<title>13&#45;&gt;12</title>
<path fill="none" stroke="black" d="M1672.63,-1588.05C1672.92,-1580.49 1673.15,-1572.35 1673.33,-1563.85"/>
<polygon fill="black" stroke="black" points="1673.33,-1563.6 1669.44,-1557.53 1673.55,-1551.6 1677.44,-1557.68 1673.33,-1563.6"/>
</g>
<!-- 14 -->
<g id="node15" class="node">
<title>14</title>
<polygon fill="#f2f2f2" stroke="black" points="144.86,-949.7 144.86,-1049.7 439.23,-1049.7 439.23,-949.7 144.86,-949.7"/>
<text text-anchor="middle" x="292.05" y="-1033.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="292.05" y="-1016.3" font-family="Times,serif" font-size="14.00">IOracleProvider</text>
<text text-anchor="middle" x="292.05" y="-999.5" font-family="Times,serif" font-size="14.00">public/flatten/ChainlinkOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="144.86,-991.3 439.23,-991.3 "/>
<text text-anchor="start" x="152.86" y="-974.7" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="152.86" y="-957.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getPrice(asset: address): uint256</text>
</g>
<!-- 15 -->
<g id="node16" class="node">
<title>15</title>
<polygon fill="#f2f2f2" stroke="black" points="1007.86,-502.3 1007.86,-635.9 1302.23,-635.9 1302.23,-502.3 1007.86,-502.3"/>
<text text-anchor="middle" x="1155.05" y="-619.3" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="1155.05" y="-602.5" font-family="Times,serif" font-size="14.00">IERC20Metadata</text>
<text text-anchor="middle" x="1155.05" y="-585.7" font-family="Times,serif" font-size="14.00">public/flatten/ChainlinkOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="1007.86,-577.5 1302.23,-577.5 "/>
<text text-anchor="start" x="1015.86" y="-560.9" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="1015.86" y="-544.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;name(): string</text>
<text text-anchor="start" x="1015.86" y="-527.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;symbol(): string</text>
<text text-anchor="start" x="1015.86" y="-510.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;decimals(): uint8</text>
</g>
<!-- 15&#45;&gt;3 -->
<g id="edge15" class="edge">
<title>15&#45;&gt;3</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1146.11,-636.13C1138.34,-693.58 1126.79,-778.92 1116.85,-852.38"/>
<polygon fill="none" stroke="black" points="1106.39,-851.35 1112.77,-882.48 1127.2,-854.16 1106.39,-851.35"/>
</g>
<!-- 16 -->
<g id="node17" class="node">
<title>16</title>
<polygon fill="#f2f2f2" stroke="black" points="1329.42,-916.1 1329.42,-1083.3 1638.67,-1083.3 1638.67,-916.1 1329.42,-916.1"/>
<text text-anchor="middle" x="1484.05" y="-1066.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Abstract&gt;&gt;</text>
<text text-anchor="middle" x="1484.05" y="-1049.9" font-family="Times,serif" font-size="14.00">ContextUpgradeable</text>
<text text-anchor="middle" x="1484.05" y="-1033.1" font-family="Times,serif" font-size="14.00">public/flatten/ChainlinkOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="1329.42,-1024.9 1638.67,-1024.9 "/>
<text text-anchor="start" x="1337.42" y="-1008.3" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="1337.42" y="-991.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;__Context_init() &lt;&lt;onlyInitializing&gt;&gt;</text>
<text text-anchor="start" x="1337.42" y="-974.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;__Context_init_unchained() &lt;&lt;onlyInitializing&gt;&gt;</text>
<text text-anchor="start" x="1337.42" y="-957.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_msgSender(): address</text>
<text text-anchor="start" x="1337.42" y="-941.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_msgData(): bytes</text>
<text text-anchor="start" x="1337.42" y="-924.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_contextSuffixLength(): uint256</text>
</g>
<!-- 16&#45;&gt;12 -->
<g id="edge16" class="edge">
<title>16&#45;&gt;12</title>
<path fill="none" stroke="black" d="M1522.41,-1083.5C1540.07,-1121.62 1561.77,-1168.46 1582.94,-1214.15"/>
<polygon fill="none" stroke="black" points="1573.52,-1218.79 1595.65,-1241.6 1592.57,-1209.97 1573.52,-1218.79"/>
</g>
<!-- 17 -->
<g id="node18" class="node">
<title>17</title>
<polygon fill="#f2f2f2" stroke="black" points="1320.58,-405.9 1320.58,-732.3 1805.51,-732.3 1805.51,-405.9 1320.58,-405.9"/>
<text text-anchor="middle" x="1563.05" y="-715.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Abstract&gt;&gt;</text>
<text text-anchor="middle" x="1563.05" y="-698.9" font-family="Times,serif" font-size="14.00">OwnableUpgradeable</text>
<text text-anchor="middle" x="1563.05" y="-682.1" font-family="Times,serif" font-size="14.00">public/flatten/ChainlinkOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="1320.58,-673.9 1805.51,-673.9 "/>
<text text-anchor="start" x="1328.58" y="-657.3" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="1328.58" y="-640.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;OwnableStorageLocation: bytes32</text>
<polyline fill="none" stroke="black" points="1320.58,-632.3 1805.51,-632.3 "/>
<text text-anchor="start" x="1328.58" y="-615.7" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="1328.58" y="-598.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_getOwnableStorage(): ($: OwnableStorage)</text>
<text text-anchor="start" x="1328.58" y="-582.1" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="1328.58" y="-565.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;__Ownable_init(initialOwner: address) &lt;&lt;onlyInitializing&gt;&gt;</text>
<text text-anchor="start" x="1328.58" y="-548.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;__Ownable_init_unchained(initialOwner: address) &lt;&lt;onlyInitializing&gt;&gt;</text>
<text text-anchor="start" x="1328.58" y="-531.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_checkOwner()</text>
<text text-anchor="start" x="1328.58" y="-514.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_transferOwnership(newOwner: address)</text>
<text text-anchor="start" x="1328.58" y="-498.1" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="1328.58" y="-481.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; OwnershipTransferred(previousOwner: address, newOwner: address)</text>
<text text-anchor="start" x="1328.58" y="-464.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;modifier&gt;&gt; onlyOwner()</text>
<text text-anchor="start" x="1328.58" y="-447.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;owner(): address</text>
<text text-anchor="start" x="1328.58" y="-430.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;renounceOwnership() &lt;&lt;onlyOwner&gt;&gt;</text>
<text text-anchor="start" x="1328.58" y="-414.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;transferOwnership(newOwner: address) &lt;&lt;onlyOwner&gt;&gt;</text>
</g>
<!-- 17&#45;&gt;12 -->
<g id="edge18" class="edge">
<title>17&#45;&gt;12</title>
<path fill="none" stroke="black" d="M1631.77,-732.42C1638.31,-752.86 1643.99,-773.72 1648.05,-794 1675.65,-932.15 1679.45,-1091.64 1676.86,-1211.69"/>
<polygon fill="none" stroke="black" points="1666.37,-1211.45 1676.07,-1241.72 1687.36,-1212 1666.37,-1211.45"/>
</g>
<!-- 17&#45;&gt;16 -->
<g id="edge19" class="edge">
<title>17&#45;&gt;16</title>
<path fill="none" stroke="black" d="M1533.12,-732.44C1523.67,-783.75 1513.43,-839.29 1504.76,-886.3"/>
<polygon fill="none" stroke="black" points="1494.43,-884.43 1499.32,-915.84 1515.08,-888.24 1494.43,-884.43"/>
</g>
<!-- 18 -->
<g id="node19" class="node">
<title>18</title>
<polygon fill="#f2f2f2" stroke="black" points="1694.86,-958.1 1694.86,-1041.3 1989.23,-1041.3 1989.23,-958.1 1694.86,-958.1"/>
<text text-anchor="middle" x="1842.05" y="-1024.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="1842.05" y="-1007.9" font-family="Times,serif" font-size="14.00">OwnableStorage</text>
<text text-anchor="middle" x="1842.05" y="-991.1" font-family="Times,serif" font-size="14.00">public/flatten/ChainlinkOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="1694.86,-982.9 1989.23,-982.9 "/>
<text text-anchor="start" x="1702.86" y="-966.3" font-family="Times,serif" font-size="14.00">_owner: address</text>
</g>
<!-- 17&#45;&gt;18 -->
<g id="edge20" class="edge">
<title>17&#45;&gt;18</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1662.15,-732.44C1711.87,-809.65 1769.05,-896.43 1805.4,-949.33"/>
<polygon fill="black" stroke="black" points="1802.72,-951.59 1811.28,-957.84 1808.48,-947.62 1802.72,-951.59"/>
</g>
<!-- 18&#45;&gt;17 -->
<g id="edge17" class="edge">
<title>18&#45;&gt;17</title>
<path fill="none" stroke="black" d="M1819.62,-957.84C1789.42,-908.24 1733.73,-821.44 1681.92,-742.52"/>
<polygon fill="black" stroke="black" points="1681.89,-742.47 1675.25,-739.65 1675.29,-732.44 1681.93,-735.26 1681.89,-742.47"/>
</g>
<!-- 19 -->
<g id="node20" class="node">
<title>19</title>
<polygon fill="#f2f2f2" stroke="black" points="0,-502.3 0,-635.9 584.09,-635.9 584.09,-502.3 0,-502.3"/>
<text text-anchor="middle" x="292.05" y="-619.3" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="292.05" y="-602.5" font-family="Times,serif" font-size="14.00">IChainlinkOracleProvider</text>
<text text-anchor="middle" x="292.05" y="-585.7" font-family="Times,serif" font-size="14.00">public/flatten/ChainlinkOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="0,-577.5 584.09,-577.5 "/>
<text text-anchor="start" x="8" y="-560.9" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="8" y="-544.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setAssetConfig(asset: address, feed: AggregatorV3Interface, maxStalePeriod: uint256)</text>
<text text-anchor="start" x="8" y="-527.3" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="8" y="-510.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; AssetConfigSet(asset: address, feed: AggregatorV3Interface, maxStalePeriod: uint256)</text>
</g>
<!-- 19&#45;&gt;0 -->
<g id="edge22" class="edge">
<title>19&#45;&gt;0</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M229.69,-636.24C194.75,-677.98 154.57,-735.01 136.05,-794 81.27,-968.45 37.96,-1051.09 136.05,-1205.4 163.4,-1248.44 203.34,-1281.91 247.64,-1307.89"/>
<polygon fill="black" stroke="black" points="246.13,-1311.06 256.55,-1312.99 249.61,-1304.99 246.13,-1311.06"/>
</g>
<!-- 19&#45;&gt;14 -->
<g id="edge21" class="edge">
<title>19&#45;&gt;14</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M292.05,-636.13C292.05,-712.43 292.05,-837.93 292.05,-919.44"/>
<polygon fill="none" stroke="black" points="281.55,-919.61 292.05,-949.61 302.55,-919.61 281.55,-919.61"/>
</g>
<!-- 20 -->
<g id="node21" class="node">
<title>20</title>
<polygon fill="#f2f2f2" stroke="black" points="2356.82,-794.5 2356.82,-1204.9 2805.27,-1204.9 2805.27,-794.5 2356.82,-794.5"/>
<text text-anchor="middle" x="2581.05" y="-1188.3" font-family="Times,serif" font-size="14.00">&lt;&lt;Library&gt;&gt;</text>
<text text-anchor="middle" x="2581.05" y="-1171.5" font-family="Times,serif" font-size="14.00">ERC1967Utils</text>
<text text-anchor="middle" x="2581.05" y="-1154.7" font-family="Times,serif" font-size="14.00">public/flatten/ChainlinkOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="2356.82,-1146.5 2805.27,-1146.5 "/>
<text text-anchor="start" x="2364.82" y="-1129.9" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="2364.82" y="-1113.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;IMPLEMENTATION_SLOT: bytes32</text>
<text text-anchor="start" x="2364.82" y="-1096.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;ADMIN_SLOT: bytes32</text>
<text text-anchor="start" x="2364.82" y="-1079.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;BEACON_SLOT: bytes32</text>
<polyline fill="none" stroke="black" points="2356.82,-1071.3 2805.27,-1071.3 "/>
<text text-anchor="start" x="2364.82" y="-1054.7" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="2364.82" y="-1037.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_setImplementation(newImplementation: address)</text>
<text text-anchor="start" x="2364.82" y="-1021.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_setAdmin(newAdmin: address)</text>
<text text-anchor="start" x="2364.82" y="-1004.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_setBeacon(newBeacon: address)</text>
<text text-anchor="start" x="2364.82" y="-987.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_checkNonPayable()</text>
<text text-anchor="start" x="2364.82" y="-970.7" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="2364.82" y="-953.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getImplementation(): address</text>
<text text-anchor="start" x="2364.82" y="-937.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;upgradeToAndCall(newImplementation: address, data: bytes)</text>
<text text-anchor="start" x="2364.82" y="-920.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getAdmin(): address</text>
<text text-anchor="start" x="2364.82" y="-903.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;changeAdmin(newAdmin: address)</text>
<text text-anchor="start" x="2364.82" y="-886.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getBeacon(): address</text>
<text text-anchor="start" x="2364.82" y="-869.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;upgradeBeaconToAndCall(newBeacon: address, data: bytes)</text>
<text text-anchor="start" x="2364.82" y="-853.1" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="2364.82" y="-836.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; Upgraded(implementation: address)</text>
<text text-anchor="start" x="2364.82" y="-819.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; AdminChanged(previousAdmin: address, newAdmin: address)</text>
<text text-anchor="start" x="2364.82" y="-802.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; BeaconUpgraded(beacon: address)</text>
</g>
<!-- 20&#45;&gt;2 -->
<g id="edge25" class="edge">
<title>20&#45;&gt;2</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2361.11,-1205.16C2348.2,-1217.39 2335.44,-1229.54 2323.05,-1241.4 2289.58,-1273.44 2252.61,-1309.74 2222.95,-1339.07"/>
<polygon fill="black" stroke="black" points="2220.22,-1336.85 2215.57,-1346.38 2225.14,-1341.83 2220.22,-1336.85"/>
</g>
<!-- 20&#45;&gt;4 -->
<g id="edge24" class="edge">
<title>20&#45;&gt;4</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2581.05,-1205.14C2581.05,-1227.06 2581.05,-1248.87 2581.05,-1269.48"/>
<polygon fill="black" stroke="black" points="2577.55,-1269.5 2581.05,-1279.5 2584.55,-1269.5 2577.55,-1269.5"/>
</g>
<!-- 20&#45;&gt;5 -->
<g id="edge23" class="edge">
<title>20&#45;&gt;5</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2800.98,-1205.16C2813.89,-1217.39 2826.66,-1229.54 2839.05,-1241.4 2852.21,-1254 2865.91,-1267.26 2879.5,-1280.49"/>
<polygon fill="black" stroke="black" points="2877.42,-1283.36 2887.03,-1287.84 2882.31,-1278.35 2877.42,-1283.36"/>
</g>
<!-- 21 -->
<g id="node22" class="node">
<title>21</title>
<polygon fill="#f2f2f2" stroke="black" points="1823.03,-380.7 1823.03,-757.5 2371.07,-757.5 2371.07,-380.7 1823.03,-380.7"/>
<text text-anchor="middle" x="2097.05" y="-740.9" font-family="Times,serif" font-size="14.00">&lt;&lt;Abstract&gt;&gt;</text>
<text text-anchor="middle" x="2097.05" y="-724.1" font-family="Times,serif" font-size="14.00">UUPSUpgradeable</text>
<text text-anchor="middle" x="2097.05" y="-707.3" font-family="Times,serif" font-size="14.00">public/flatten/ChainlinkOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="1823.03,-699.1 2371.07,-699.1 "/>
<text text-anchor="start" x="1831.03" y="-682.5" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="1831.03" y="-665.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;__self: address</text>
<text text-anchor="start" x="1831.03" y="-648.9" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="1831.03" y="-632.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;UPGRADE_INTERFACE_VERSION: string</text>
<polyline fill="none" stroke="black" points="1823.03,-623.9 2371.07,-623.9 "/>
<text text-anchor="start" x="1831.03" y="-607.3" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="1831.03" y="-590.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_upgradeToAndCallUUPS(newImplementation: address, data: bytes)</text>
<text text-anchor="start" x="1831.03" y="-573.7" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="1831.03" y="-556.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;abstract&gt;&gt; _authorizeUpgrade(newImplementation: address)</text>
<text text-anchor="start" x="1831.03" y="-540.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;__UUPSUpgradeable_init() &lt;&lt;onlyInitializing&gt;&gt;</text>
<text text-anchor="start" x="1831.03" y="-523.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;__UUPSUpgradeable_init_unchained() &lt;&lt;onlyInitializing&gt;&gt;</text>
<text text-anchor="start" x="1831.03" y="-506.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_checkProxy()</text>
<text text-anchor="start" x="1831.03" y="-489.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_checkNotDelegated()</text>
<text text-anchor="start" x="1831.03" y="-472.9" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="1831.03" y="-456.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;proxiableUUID(): bytes32 &lt;&lt;notDelegated&gt;&gt;</text>
<text text-anchor="start" x="1831.03" y="-439.3" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="1831.03" y="-422.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;payable&gt;&gt; upgradeToAndCall(newImplementation: address, data: bytes) &lt;&lt;onlyProxy&gt;&gt;</text>
<text text-anchor="start" x="1831.03" y="-405.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;modifier&gt;&gt; onlyProxy()</text>
<text text-anchor="start" x="1831.03" y="-388.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;modifier&gt;&gt; notDelegated()</text>
</g>
<!-- 21&#45;&gt;1 -->
<g id="edge27" class="edge">
<title>21&#45;&gt;1</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2138.65,-757.78C2151.26,-814.7 2164.44,-874.17 2174.55,-919.78"/>
<polygon fill="none" stroke="black" points="2164.35,-922.27 2181.09,-949.28 2184.85,-917.72 2164.35,-922.27"/>
</g>
<!-- 21&#45;&gt;12 -->
<g id="edge26" class="edge">
<title>21&#45;&gt;12</title>
<path fill="none" stroke="black" d="M2043.4,-757.69C2040.69,-769.93 2038.2,-782.11 2036.05,-794 2019.7,-884.34 2047.63,-1128.13 1998.05,-1205.4 1963.28,-1259.58 1907.26,-1300.03 1851.11,-1329.39"/>
<polygon fill="none" stroke="black" points="1846.34,-1320.03 1824.07,-1342.71 1855.62,-1338.87 1846.34,-1320.03"/>
</g>
<!-- 21&#45;&gt;20 -->
<g id="edge28" class="edge">
<title>21&#45;&gt;20</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2308.98,-757.78C2322.16,-769.44 2335.45,-781.22 2348.71,-792.96"/>
<polygon fill="black" stroke="black" points="2346.7,-795.85 2356.51,-799.86 2351.34,-790.61 2346.7,-795.85"/>
</g>
<!-- 22 -->
<g id="node23" class="node">
<title>22</title>
<polygon fill="#f2f2f2" stroke="black" points="535.83,-0.5 535.83,-343.7 1150.26,-343.7 1150.26,-0.5 535.83,-0.5"/>
<text text-anchor="middle" x="843.05" y="-327.1" font-family="Times,serif" font-size="14.00">ChainlinkOracleProvider</text>
<text text-anchor="middle" x="843.05" y="-310.3" font-family="Times,serif" font-size="14.00">public/flatten/ChainlinkOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="535.83,-302.1 1150.26,-302.1 "/>
<text text-anchor="start" x="543.83" y="-285.5" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="543.83" y="-268.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;GRACE_PERIOD_TIME: uint256</text>
<text text-anchor="start" x="543.83" y="-251.9" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="543.83" y="-235.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;_ORACLE_PROVIDER_STORAGE: bytes32</text>
<text text-anchor="start" x="543.83" y="-218.3" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="543.83" y="-201.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;sequencerUptimeFeed: AggregatorV3Interface</text>
<text text-anchor="start" x="543.83" y="-184.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;initialOwner: address</text>
<polyline fill="none" stroke="black" points="535.83,-176.5 1150.26,-176.5 "/>
<text text-anchor="start" x="543.83" y="-159.9" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="543.83" y="-143.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_authorizeUpgrade(newImplementation: address) &lt;&lt;onlyOwner&gt;&gt;</text>
<text text-anchor="start" x="543.83" y="-126.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_validateSequencerStatus()</text>
<text text-anchor="start" x="543.83" y="-109.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_getProviderStorage(): (data: OracleProviderStorage)</text>
<text text-anchor="start" x="543.83" y="-92.7" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="543.83" y="-75.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;setAssetConfig(asset: address, feed: AggregatorV3Interface, maxStalePeriod: uint256) &lt;&lt;onlyOwner&gt;&gt;</text>
<text text-anchor="start" x="543.83" y="-59.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getPrice(asset: address): uint256</text>
<text text-anchor="start" x="543.83" y="-42.3" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="543.83" y="-25.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;constructor(_sequencerUptimeFeed: AggregatorV3Interface, _initialOwner: address)</text>
<text text-anchor="start" x="543.83" y="-8.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;initialize() &lt;&lt;initializer&gt;&gt;</text>
</g>
<!-- 22&#45;&gt;0 -->
<g id="edge34" class="edge">
<title>22&#45;&gt;0</title>
<path fill="none" stroke="black" d="M670.71,-343.82C663.02,-355.65 656.03,-367.81 650.05,-380.2 576.19,-533.11 665.1,-604.23 593.05,-758 583.96,-777.38 568.97,-774.11 561.05,-794 493.61,-963.28 509.22,-1181.07 526.03,-1302.78"/>
<polygon fill="black" stroke="black" points="522.58,-1303.4 527.45,-1312.81 529.52,-1302.42 522.58,-1303.4"/>
</g>
<!-- 22&#45;&gt;15 -->
<g id="edge36" class="edge">
<title>22&#45;&gt;15</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M977.85,-343.77C1019.35,-396.3 1062.88,-451.42 1096.41,-493.87"/>
<polygon fill="black" stroke="black" points="1093.82,-496.24 1102.77,-501.91 1099.32,-491.9 1093.82,-496.24"/>
</g>
<!-- 22&#45;&gt;17 -->
<g id="edge33" class="edge">
<title>22&#45;&gt;17</title>
<path fill="none" stroke="black" d="M1150.4,-297.27C1204.99,-322.64 1260.51,-350.68 1311.05,-380.2 1316.28,-383.26 1321.53,-386.4 1326.79,-389.62"/>
<polygon fill="none" stroke="black" points="1321.28,-398.56 1352.25,-405.72 1332.5,-380.81 1321.28,-398.56"/>
</g>
<!-- 22&#45;&gt;19 -->
<g id="edge31" class="edge">
<title>22&#45;&gt;19</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M604.98,-343.77C536.9,-392.57 465.72,-443.6 408.48,-484.64"/>
<polygon fill="none" stroke="black" points="402.3,-476.14 384.03,-502.16 414.53,-493.21 402.3,-476.14"/>
</g>
<!-- 22&#45;&gt;21 -->
<g id="edge32" class="edge">
<title>22&#45;&gt;21</title>
<path fill="none" stroke="black" d="M1150.47,-208.53C1337.82,-237.05 1580.18,-286.45 1794.95,-372.54"/>
<polygon fill="none" stroke="black" points="1791.28,-382.38 1823.02,-384.07 1799.26,-362.96 1791.28,-382.38"/>
</g>
<!-- 23 -->
<g id="node24" class="node">
<title>23</title>
<polygon fill="#f2f2f2" stroke="black" points="695.86,-527.5 695.86,-610.7 990.23,-610.7 990.23,-527.5 695.86,-527.5"/>
<text text-anchor="middle" x="843.05" y="-594.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="843.05" y="-577.3" font-family="Times,serif" font-size="14.00">OracleProviderStorage</text>
<text text-anchor="middle" x="843.05" y="-560.5" font-family="Times,serif" font-size="14.00">public/flatten/ChainlinkOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="695.86,-552.3 990.23,-552.3 "/>
<text text-anchor="start" x="703.86" y="-535.7" font-family="Times,serif" font-size="14.00">configs: mapping(address=&gt;AssetConfig)</text>
</g>
<!-- 22&#45;&gt;23 -->
<g id="edge37" class="edge">
<title>22&#45;&gt;23</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M836.35,-343.77C836.09,-406.55 836.75,-473.01 838.34,-517.15"/>
<polygon fill="black" stroke="black" points="834.85,-517.47 838.74,-527.32 841.84,-517.19 834.85,-517.47"/>
</g>
<!-- 24 -->
<g id="node25" class="node">
<title>24</title>
<polygon fill="#f2f2f2" stroke="black" points="569.86,-949.7 569.86,-1049.7 864.23,-1049.7 864.23,-949.7 569.86,-949.7"/>
<text text-anchor="middle" x="717.05" y="-1033.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="717.05" y="-1016.3" font-family="Times,serif" font-size="14.00">AssetConfig</text>
<text text-anchor="middle" x="717.05" y="-999.5" font-family="Times,serif" font-size="14.00">public/flatten/ChainlinkOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="569.86,-991.3 864.23,-991.3 "/>
<text text-anchor="start" x="577.86" y="-974.7" font-family="Times,serif" font-size="14.00">feed: AggregatorV3Interface</text>
<text text-anchor="start" x="577.86" y="-957.9" font-family="Times,serif" font-size="14.00">maxStalePeriod: uint256</text>
</g>
<!-- 22&#45;&gt;24 -->
<g id="edge35" class="edge">
<title>22&#45;&gt;24</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M693.23,-343.94C687.3,-355.8 682.15,-367.93 678.05,-380.2 612.42,-576.4 662.41,-827.13 695.1,-939.54"/>
<polygon fill="black" stroke="black" points="691.81,-940.74 698.01,-949.33 698.52,-938.75 691.81,-940.74"/>
</g>
<!-- 23&#45;&gt;22 -->
<g id="edge29" class="edge">
<title>23&#45;&gt;22</title>
<path fill="none" stroke="black" d="M847.35,-527.32C849.11,-486.33 849.92,-420.21 849.78,-356.13"/>
<polygon fill="black" stroke="black" points="849.78,-355.77 845.76,-349.78 849.74,-343.77 853.76,-349.76 849.78,-355.77"/>
</g>
<!-- 23&#45;&gt;24 -->
<g id="edge38" class="edge">
<title>23&#45;&gt;24</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M831.08,-610.81C808.67,-687.04 760.3,-851.58 734.34,-939.87"/>
<polygon fill="black" stroke="black" points="730.98,-938.89 731.52,-949.47 737.7,-940.86 730.98,-938.89"/>
</g>
<!-- 24&#45;&gt;0 -->
<g id="edge39" class="edge">
<title>24&#45;&gt;0</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M695.24,-1049.93C667.19,-1113.22 617.94,-1224.4 582.85,-1303.6"/>
<polygon fill="black" stroke="black" points="579.5,-1302.53 578.64,-1313.09 585.9,-1305.37 579.5,-1302.53"/>
</g>
<!-- 24&#45;&gt;22 -->
<g id="edge30" class="edge">
<title>24&#45;&gt;22</title>
<path fill="none" stroke="black" d="M707.65,-949.33C683.54,-842.32 628.44,-582.32 696.05,-380.2 698.9,-371.67 702.26,-363.2 706.03,-354.85"/>
<polygon fill="black" stroke="black" points="706.06,-354.78 705.01,-347.64 711.2,-343.94 712.24,-351.07 706.06,-354.78"/>
</g>
</g>
</svg>
