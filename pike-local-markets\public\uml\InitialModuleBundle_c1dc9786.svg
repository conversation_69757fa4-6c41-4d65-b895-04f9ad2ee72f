<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 5.0.0 (20220707.1540)
 -->
<!-- Title: UmlClassDiagram Pages: 1 -->
<svg width="2088pt" height="973pt"
 viewBox="0.00 0.00 2088.00 972.80" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 968.8)">
<title>UmlClassDiagram</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-968.8 2084,-968.8 2084,4 -4,4"/>
<!-- 0 -->
<g id="node1" class="node">
<title>0</title>
<polygon fill="#f2f2f2" stroke="black" points="145.64,-137.5 145.64,-447.1 459.41,-447.1 459.41,-137.5 145.64,-137.5"/>
<text text-anchor="middle" x="302.53" y="-430.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Abstract&gt;&gt;</text>
<text text-anchor="middle" x="302.53" y="-413.7" font-family="Times,serif" font-size="14.00">Initializable</text>
<text text-anchor="middle" x="302.53" y="-396.9" font-family="Times,serif" font-size="14.00">public/flatten/InitialModuleBundle.flatten.sol</text>
<polyline fill="none" stroke="black" points="145.64,-388.7 459.41,-388.7 "/>
<text text-anchor="start" x="153.64" y="-372.1" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="153.64" y="-355.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;INITIALIZABLE_STORAGE: bytes32</text>
<polyline fill="none" stroke="black" points="145.64,-347.1 459.41,-347.1 "/>
<text text-anchor="start" x="153.64" y="-330.5" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="153.64" y="-313.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_getInitializableStorage(): ($: InitializableStorage)</text>
<text text-anchor="start" x="153.64" y="-296.9" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="153.64" y="-280.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_checkInitializing()</text>
<text text-anchor="start" x="153.64" y="-263.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_disableInitializers()</text>
<text text-anchor="start" x="153.64" y="-246.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_getInitializedVersion(): uint64</text>
<text text-anchor="start" x="153.64" y="-229.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_isInitializing(): bool</text>
<text text-anchor="start" x="153.64" y="-212.9" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="153.64" y="-196.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; Initialized(version: uint64)</text>
<text text-anchor="start" x="153.64" y="-179.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;modifier&gt;&gt; initializer()</text>
<text text-anchor="start" x="153.64" y="-162.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;modifier&gt;&gt; reinitializer(version: uint64)</text>
<text text-anchor="start" x="153.64" y="-145.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;modifier&gt;&gt; onlyInitializing()</text>
</g>
<!-- 1 -->
<g id="node2" class="node">
<title>1</title>
<polygon fill="#f2f2f2" stroke="black" points="0,-559.7 0,-659.7 271.06,-659.7 271.06,-559.7 0,-559.7"/>
<text text-anchor="middle" x="135.53" y="-643.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="135.53" y="-626.3" font-family="Times,serif" font-size="14.00">InitializableStorage</text>
<text text-anchor="middle" x="135.53" y="-609.5" font-family="Times,serif" font-size="14.00">public/flatten/InitialModuleBundle.flatten.sol</text>
<polyline fill="none" stroke="black" points="0,-601.3 271.06,-601.3 "/>
<text text-anchor="start" x="8" y="-584.7" font-family="Times,serif" font-size="14.00">_initialized: uint64</text>
<text text-anchor="start" x="8" y="-567.9" font-family="Times,serif" font-size="14.00">_initializing: bool</text>
</g>
<!-- 0&#45;&gt;1 -->
<g id="edge2" class="edge">
<title>0&#45;&gt;1</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M214.24,-447.3C194.78,-483.99 175.67,-520.94 161.11,-550.22"/>
<polygon fill="black" stroke="black" points="157.84,-548.93 156.55,-559.44 164.12,-552.03 157.84,-548.93"/>
</g>
<!-- 1&#45;&gt;0 -->
<g id="edge1" class="edge">
<title>1&#45;&gt;0</title>
<path fill="none" stroke="black" d="M166.67,-559.44C182.42,-531.69 202.28,-495.24 221.93,-458.26"/>
<polygon fill="black" stroke="black" points="222.11,-457.91 221.39,-450.73 227.73,-447.3 228.46,-454.48 222.11,-457.91"/>
</g>
<!-- 2 -->
<g id="node3" class="node">
<title>2</title>
<polygon fill="#f2f2f2" stroke="black" points="1024,-21.3 1024,-79.7 1295.06,-79.7 1295.06,-21.3 1024,-21.3"/>
<text text-anchor="middle" x="1159.53" y="-63.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Library&gt;&gt;</text>
<text text-anchor="middle" x="1159.53" y="-46.3" font-family="Times,serif" font-size="14.00">CommonError</text>
<text text-anchor="middle" x="1159.53" y="-29.5" font-family="Times,serif" font-size="14.00">public/flatten/InitialModuleBundle.flatten.sol</text>
</g>
<!-- 3 -->
<g id="node4" class="node">
<title>3</title>
<polygon fill="#f2f2f2" stroke="black" points="288.66,-484.1 288.66,-735.3 704.4,-735.3 704.4,-484.1 288.66,-484.1"/>
<text text-anchor="middle" x="496.53" y="-718.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="496.53" y="-701.9" font-family="Times,serif" font-size="14.00">IOwnable</text>
<text text-anchor="middle" x="496.53" y="-685.1" font-family="Times,serif" font-size="14.00">public/flatten/InitialModuleBundle.flatten.sol</text>
<polyline fill="none" stroke="black" points="288.66,-676.9 704.4,-676.9 "/>
<text text-anchor="start" x="296.66" y="-660.3" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="296.66" y="-643.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;acceptOwnership()</text>
<text text-anchor="start" x="296.66" y="-626.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;transferOwnership(newOwner: address)</text>
<text text-anchor="start" x="296.66" y="-609.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;nominateNewOwner(newNominatedOwner: address)</text>
<text text-anchor="start" x="296.66" y="-593.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;renounceNomination()</text>
<text text-anchor="start" x="296.66" y="-576.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;renounceOwnership()</text>
<text text-anchor="start" x="296.66" y="-559.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;owner(): address</text>
<text text-anchor="start" x="296.66" y="-542.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;pendingOwner(): address</text>
<text text-anchor="start" x="296.66" y="-525.9" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="296.66" y="-509.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; OwnerNominated(newOwner: address)</text>
<text text-anchor="start" x="296.66" y="-492.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; OwnerChanged(oldOwner: address, newOwner: address)</text>
</g>
<!-- 4 -->
<g id="node5" class="node">
<title>4</title>
<polygon fill="#f2f2f2" stroke="black" points="1299.65,-526.1 1299.65,-693.3 1673.41,-693.3 1673.41,-526.1 1299.65,-526.1"/>
<text text-anchor="middle" x="1486.53" y="-676.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="1486.53" y="-659.9" font-family="Times,serif" font-size="14.00">IUpgrade</text>
<text text-anchor="middle" x="1486.53" y="-643.1" font-family="Times,serif" font-size="14.00">public/flatten/InitialModuleBundle.flatten.sol</text>
<polyline fill="none" stroke="black" points="1299.65,-634.9 1673.41,-634.9 "/>
<text text-anchor="start" x="1307.65" y="-618.3" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="1307.65" y="-601.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;upgradeTo(newImplementation: address)</text>
<text text-anchor="start" x="1307.65" y="-584.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;simulateUpgradeTo(newImplementation: address)</text>
<text text-anchor="start" x="1307.65" y="-567.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getImplementation(): address</text>
<text text-anchor="start" x="1307.65" y="-551.1" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="1307.65" y="-534.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; Upgraded(self: address, implementation: address)</text>
</g>
<!-- 5 -->
<g id="node6" class="node">
<title>5</title>
<polygon fill="#f2f2f2" stroke="black" points="990.01,-772.3 990.01,-964.3 1303.04,-964.3 1303.04,-772.3 990.01,-772.3"/>
<text text-anchor="middle" x="1146.53" y="-947.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Abstract&gt;&gt;</text>
<text text-anchor="middle" x="1146.53" y="-930.9" font-family="Times,serif" font-size="14.00">OwnableStorage</text>
<text text-anchor="middle" x="1146.53" y="-914.1" font-family="Times,serif" font-size="14.00">public/flatten/InitialModuleBundle.flatten.sol</text>
<polyline fill="none" stroke="black" points="990.01,-905.9 1303.04,-905.9 "/>
<text text-anchor="start" x="998.01" y="-889.3" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="998.01" y="-872.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;_SLOT_OWNABLE_STORAGE: bytes32</text>
<polyline fill="none" stroke="black" points="990.01,-864.3 1303.04,-864.3 "/>
<text text-anchor="start" x="998.01" y="-847.7" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="998.01" y="-830.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_checkOwner()</text>
<text text-anchor="start" x="998.01" y="-814.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_owner(): address</text>
<text text-anchor="start" x="998.01" y="-797.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_pendingOwner(): address</text>
<text text-anchor="start" x="998.01" y="-780.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_getOwnableStorage(): ($: Ownable2StepStorage)</text>
</g>
<!-- 6 -->
<g id="node7" class="node">
<title>6</title>
<polygon fill="#f2f2f2" stroke="black" points="722,-559.7 722,-659.7 993.06,-659.7 993.06,-559.7 722,-559.7"/>
<text text-anchor="middle" x="857.53" y="-643.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="857.53" y="-626.3" font-family="Times,serif" font-size="14.00">Ownable2StepStorage</text>
<text text-anchor="middle" x="857.53" y="-609.5" font-family="Times,serif" font-size="14.00">public/flatten/InitialModuleBundle.flatten.sol</text>
<polyline fill="none" stroke="black" points="722,-601.3 993.06,-601.3 "/>
<text text-anchor="start" x="730" y="-584.7" font-family="Times,serif" font-size="14.00">owner: address</text>
<text text-anchor="start" x="730" y="-567.9" font-family="Times,serif" font-size="14.00">pendingOwner: address</text>
</g>
<!-- 5&#45;&gt;6 -->
<g id="edge4" class="edge">
<title>5&#45;&gt;6</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1045.79,-772.04C1006.31,-736.59 962.02,-697.53 926.31,-666.72"/>
<polygon fill="black" stroke="black" points="928.22,-663.74 918.35,-659.87 923.65,-669.05 928.22,-663.74"/>
</g>
<!-- 6&#45;&gt;5 -->
<g id="edge3" class="edge">
<title>6&#45;&gt;5</title>
<path fill="none" stroke="black" d="M907.46,-659.87C939.55,-689.37 982.68,-728.01 1023.48,-763.94"/>
<polygon fill="black" stroke="black" points="1023.69,-764.12 1030.83,-765.08 1032.7,-772.04 1025.55,-771.09 1023.69,-764.12"/>
</g>
<!-- 7 -->
<g id="node8" class="node">
<title>7</title>
<polygon fill="#f2f2f2" stroke="black" points="1729.46,-547.3 1729.46,-672.1 2031.59,-672.1 2031.59,-547.3 1729.46,-547.3"/>
<text text-anchor="middle" x="1880.53" y="-655.5" font-family="Times,serif" font-size="14.00">UpgradeStorage</text>
<text text-anchor="middle" x="1880.53" y="-638.7" font-family="Times,serif" font-size="14.00">public/flatten/InitialModuleBundle.flatten.sol</text>
<polyline fill="none" stroke="black" points="1729.46,-630.5 2031.59,-630.5 "/>
<text text-anchor="start" x="1737.46" y="-613.9" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="1737.46" y="-597.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;_IMPLEMENTATION_SLOT: bytes32</text>
<polyline fill="none" stroke="black" points="1729.46,-588.9 2031.59,-588.9 "/>
<text text-anchor="start" x="1737.46" y="-572.3" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="1737.46" y="-555.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_getImplementationStorage(): (data: ProxyData)</text>
</g>
<!-- 8 -->
<g id="node9" class="node">
<title>8</title>
<polygon fill="#f2f2f2" stroke="black" points="1745,-818.3 1745,-918.3 2016.06,-918.3 2016.06,-818.3 1745,-818.3"/>
<text text-anchor="middle" x="1880.53" y="-901.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="1880.53" y="-884.9" font-family="Times,serif" font-size="14.00">ProxyData</text>
<text text-anchor="middle" x="1880.53" y="-868.1" font-family="Times,serif" font-size="14.00">public/flatten/InitialModuleBundle.flatten.sol</text>
<polyline fill="none" stroke="black" points="1745,-859.9 2016.06,-859.9 "/>
<text text-anchor="start" x="1753" y="-843.3" font-family="Times,serif" font-size="14.00">implementation: address</text>
<text text-anchor="start" x="1753" y="-826.5" font-family="Times,serif" font-size="14.00">simulatingUpgrade: bool</text>
</g>
<!-- 7&#45;&gt;8 -->
<g id="edge6" class="edge">
<title>7&#45;&gt;8</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1874.67,-672.16C1873.46,-713.3 1873.48,-767.17 1874.73,-807.88"/>
<polygon fill="black" stroke="black" points="1871.23,-808.06 1875.07,-817.93 1878.23,-807.82 1871.23,-808.06"/>
</g>
<!-- 8&#45;&gt;7 -->
<g id="edge5" class="edge">
<title>8&#45;&gt;7</title>
<path fill="none" stroke="black" d="M1885.98,-817.93C1887.42,-780.04 1887.66,-727.13 1886.7,-684.25"/>
<polygon fill="black" stroke="black" points="1886.7,-684.16 1882.54,-678.26 1886.39,-672.16 1890.54,-678.06 1886.7,-684.16"/>
</g>
<!-- 9 -->
<g id="node10" class="node">
<title>9</title>
<polygon fill="#f2f2f2" stroke="black" points="1011,-568.1 1011,-651.3 1282.06,-651.3 1282.06,-568.1 1011,-568.1"/>
<text text-anchor="middle" x="1146.53" y="-634.7" font-family="Times,serif" font-size="14.00">OwnableMixin</text>
<text text-anchor="middle" x="1146.53" y="-617.9" font-family="Times,serif" font-size="14.00">public/flatten/InitialModuleBundle.flatten.sol</text>
<polyline fill="none" stroke="black" points="1011,-609.7 1282.06,-609.7 "/>
<text text-anchor="start" x="1019" y="-593.1" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="1019" y="-576.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;modifier&gt;&gt; onlyOwner()</text>
</g>
<!-- 9&#45;&gt;5 -->
<g id="edge7" class="edge">
<title>9&#45;&gt;5</title>
<path fill="none" stroke="black" d="M1146.53,-651.36C1146.53,-676.13 1146.53,-709.24 1146.53,-742.09"/>
<polygon fill="none" stroke="black" points="1136.03,-742.25 1146.53,-772.25 1157.03,-742.25 1136.03,-742.25"/>
</g>
<!-- 10 -->
<g id="node11" class="node">
<title>10</title>
<polygon fill="#f2f2f2" stroke="black" points="644.02,-191.9 644.02,-392.7 1071.04,-392.7 1071.04,-191.9 644.02,-191.9"/>
<text text-anchor="middle" x="857.53" y="-376.1" font-family="Times,serif" font-size="14.00">OwnableModule</text>
<text text-anchor="middle" x="857.53" y="-359.3" font-family="Times,serif" font-size="14.00">public/flatten/InitialModuleBundle.flatten.sol</text>
<polyline fill="none" stroke="black" points="644.02,-351.1 1071.04,-351.1 "/>
<text text-anchor="start" x="652.02" y="-334.5" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="652.02" y="-317.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;transferOwnership(newOwner: address) &lt;&lt;onlyOwner&gt;&gt;</text>
<text text-anchor="start" x="652.02" y="-300.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;renounceNomination()</text>
<text text-anchor="start" x="652.02" y="-284.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;renounceOwnership() &lt;&lt;onlyOwner&gt;&gt;</text>
<text text-anchor="start" x="652.02" y="-267.3" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="652.02" y="-250.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;acceptOwnership()</text>
<text text-anchor="start" x="652.02" y="-233.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;nominateNewOwner(newNominatedOwner: address) &lt;&lt;onlyOwner&gt;&gt;</text>
<text text-anchor="start" x="652.02" y="-216.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;owner(): address</text>
<text text-anchor="start" x="652.02" y="-200.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;pendingOwner(): address</text>
</g>
<!-- 10&#45;&gt;3 -->
<g id="edge8" class="edge">
<title>10&#45;&gt;3</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M743.31,-393.09C717.48,-415.65 689.56,-440.05 662.1,-464.04"/>
<polygon fill="none" stroke="black" points="654.89,-456.4 639.2,-484.05 668.71,-472.21 654.89,-456.4"/>
</g>
<!-- 10&#45;&gt;6 -->
<g id="edge10" class="edge">
<title>10&#45;&gt;6</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M857.53,-393.09C857.53,-444.39 857.53,-505.17 857.53,-549.1"/>
<polygon fill="black" stroke="black" points="854.03,-549.33 857.53,-559.33 861.03,-549.33 854.03,-549.33"/>
</g>
<!-- 10&#45;&gt;9 -->
<g id="edge9" class="edge">
<title>10&#45;&gt;9</title>
<path fill="none" stroke="black" d="M948.97,-393.09C994.7,-443 1048.67,-501.9 1088.63,-545.51"/>
<polygon fill="none" stroke="black" points="1080.96,-552.68 1108.97,-567.71 1096.44,-538.5 1080.96,-552.68"/>
</g>
<!-- 11 -->
<g id="node12" class="node">
<title>11</title>
<polygon fill="#f2f2f2" stroke="black" points="1499.14,-191.9 1499.14,-392.7 1903.92,-392.7 1903.92,-191.9 1499.14,-191.9"/>
<text text-anchor="middle" x="1701.53" y="-376.1" font-family="Times,serif" font-size="14.00">UpgradeModule</text>
<text text-anchor="middle" x="1701.53" y="-359.3" font-family="Times,serif" font-size="14.00">public/flatten/InitialModuleBundle.flatten.sol</text>
<polyline fill="none" stroke="black" points="1499.14,-351.1 1903.92,-351.1 "/>
<text text-anchor="start" x="1507.14" y="-334.5" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="1507.14" y="-317.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_upgradeTo(newImplementation: address)</text>
<text text-anchor="start" x="1507.14" y="-300.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_implementationIsSterile(candidateImplementation: address): bool</text>
<text text-anchor="start" x="1507.14" y="-284.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;isContract(account: address): bool</text>
<text text-anchor="start" x="1507.14" y="-267.3" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="1507.14" y="-250.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getImplementation(): address</text>
<text text-anchor="start" x="1507.14" y="-233.7" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="1507.14" y="-216.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;upgradeTo(newImplementation: address)</text>
<text text-anchor="start" x="1507.14" y="-200.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;simulateUpgradeTo(newImplementation: address)</text>
</g>
<!-- 11&#45;&gt;4 -->
<g id="edge11" class="edge">
<title>11&#45;&gt;4</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1633.5,-393.09C1610.1,-427.43 1583.79,-466.02 1559.97,-500.96"/>
<polygon fill="none" stroke="black" points="1551.17,-495.23 1542.95,-525.94 1568.52,-507.06 1551.17,-495.23"/>
</g>
<!-- 11&#45;&gt;5 -->
<g id="edge13" class="edge">
<title>11&#45;&gt;5</title>
<path fill="none" stroke="black" d="M1710.6,-392.95C1718.76,-507.23 1723.9,-685.32 1682.53,-735.8 1638.12,-789.98 1469.56,-825.17 1333.17,-845.39"/>
<polygon fill="none" stroke="black" points="1331.49,-835.02 1303.28,-849.66 1334.47,-855.81 1331.49,-835.02"/>
</g>
<!-- 11&#45;&gt;7 -->
<g id="edge12" class="edge">
<title>11&#45;&gt;7</title>
<path fill="none" stroke="black" d="M1758.16,-393.09C1781.47,-434.15 1808.22,-481.29 1830.76,-521"/>
<polygon fill="none" stroke="black" points="1821.65,-526.23 1845.59,-547.14 1839.91,-515.87 1821.65,-526.23"/>
</g>
<!-- 11&#45;&gt;8 -->
<g id="edge14" class="edge">
<title>11&#45;&gt;8</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1904.25,-362.74C1957.98,-391.45 2009.58,-430.74 2040.53,-483.6 2097.16,-580.33 2088.42,-634.46 2040.53,-735.8 2026.26,-766 2001.28,-791.59 1975.24,-811.95"/>
<polygon fill="black" stroke="black" points="1972.99,-809.27 1967.13,-818.1 1977.22,-814.85 1972.99,-809.27"/>
</g>
<!-- 12 -->
<g id="node13" class="node">
<title>12</title>
<polygon fill="#f2f2f2" stroke="black" points="708.95,-0.5 708.95,-100.5 1006.11,-100.5 1006.11,-0.5 708.95,-0.5"/>
<text text-anchor="middle" x="857.53" y="-83.9" font-family="Times,serif" font-size="14.00">InitialModuleBundle</text>
<text text-anchor="middle" x="857.53" y="-67.1" font-family="Times,serif" font-size="14.00">public/flatten/InitialModuleBundle.flatten.sol</text>
<polyline fill="none" stroke="black" points="708.95,-58.9 1006.11,-58.9 "/>
<text text-anchor="start" x="716.95" y="-42.3" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="716.95" y="-25.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;constructor()</text>
<text text-anchor="start" x="716.95" y="-8.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;initialize(initialOwner: address) &lt;&lt;initializer&gt;&gt;</text>
</g>
<!-- 12&#45;&gt;0 -->
<g id="edge17" class="edge">
<title>12&#45;&gt;0</title>
<path fill="none" stroke="black" d="M725.15,-100.53C695.19,-112.1 663.64,-124.67 634.53,-137 586.22,-157.46 534.56,-180.76 486.72,-202.95"/>
<polygon fill="none" stroke="black" points="482.19,-193.48 459.43,-215.67 491.06,-212.51 482.19,-193.48"/>
</g>
<!-- 12&#45;&gt;10 -->
<g id="edge15" class="edge">
<title>12&#45;&gt;10</title>
<path fill="none" stroke="black" d="M857.53,-100.61C857.53,-118.59 857.53,-139.89 857.53,-161.61"/>
<polygon fill="none" stroke="black" points="847.03,-161.77 857.53,-191.77 868.03,-161.77 847.03,-161.77"/>
</g>
<!-- 12&#45;&gt;11 -->
<g id="edge16" class="edge">
<title>12&#45;&gt;11</title>
<path fill="none" stroke="black" d="M1006.24,-98.53C1009.02,-99.37 1011.79,-100.19 1014.53,-101 1165.56,-145.71 1336.21,-193 1469.65,-229.25"/>
<polygon fill="none" stroke="black" points="1467.13,-239.45 1498.84,-237.17 1472.63,-219.18 1467.13,-239.45"/>
</g>
</g>
</svg>
