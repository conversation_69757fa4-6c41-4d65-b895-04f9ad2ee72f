<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 5.0.0 (20220707.1540)
 -->
<!-- Title: UmlClassDiagram Pages: 1 -->
<svg width="1242pt" height="1295pt"
 viewBox="0.00 0.00 1241.64 1294.60" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 1290.6)">
<title>UmlClassDiagram</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-1290.6 1237.64,-1290.6 1237.64,4 -4,4"/>
<!-- 0 -->
<g id="node1" class="node">
<title>0</title>
<polygon fill="#f2f2f2" stroke="black" points="21.47,-80.1 21.47,-138.5 256.77,-138.5 256.77,-80.1 21.47,-80.1"/>
<text text-anchor="middle" x="139.12" y="-121.9" font-family="Times,serif" font-size="14.00">&lt;&lt;Library&gt;&gt;</text>
<text text-anchor="middle" x="139.12" y="-105.1" font-family="Times,serif" font-size="14.00">CommonError</text>
<text text-anchor="middle" x="139.12" y="-88.3" font-family="Times,serif" font-size="14.00">public/flatten/RBACModule.flatten.sol</text>
</g>
<!-- 1 -->
<g id="node2" class="node">
<title>1</title>
<polygon fill="#f2f2f2" stroke="black" points="0,-255.1 0,-523.1 594.25,-523.1 594.25,-255.1 0,-255.1"/>
<text text-anchor="middle" x="297.12" y="-506.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="297.12" y="-489.7" font-family="Times,serif" font-size="14.00">IRBAC</text>
<text text-anchor="middle" x="297.12" y="-472.9" font-family="Times,serif" font-size="14.00">public/flatten/RBACModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="0,-464.7 594.25,-464.7 "/>
<text text-anchor="start" x="8" y="-448.1" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="8" y="-431.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;grantPermission(permission: bytes32, target: address)</text>
<text text-anchor="start" x="8" y="-414.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;grantNestedPermission(permission: bytes32, nestedAddress: address, target: address)</text>
<text text-anchor="start" x="8" y="-397.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;revokePermission(permission: bytes32, target: address)</text>
<text text-anchor="start" x="8" y="-380.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;revokeNestedPermission(permission: bytes32, nestedAddress: address, target: address)</text>
<text text-anchor="start" x="8" y="-364.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;hasPermission(permission: bytes32, target: address): bool</text>
<text text-anchor="start" x="8" y="-347.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;hasNestedPermission(permission: bytes32, nestedAddress: address, target: address): bool</text>
<text text-anchor="start" x="8" y="-330.5" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="8" y="-313.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; PermissionGranted(permission: bytes32, target: address)</text>
<text text-anchor="start" x="8" y="-296.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NestedPermissionGranted(permission: bytes32, nestedAddress: address, target: address)</text>
<text text-anchor="start" x="8" y="-280.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; PermissionRevoked(permission: bytes32, target: address)</text>
<text text-anchor="start" x="8" y="-263.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NestedPermissionRevoked(permission: bytes32, nestedAddress: address, target: address)</text>
</g>
<!-- 2 -->
<g id="node3" class="node">
<title>2</title>
<polygon fill="#f2f2f2" stroke="black" points="920.61,-957.1 920.61,-1149.1 1233.64,-1149.1 1233.64,-957.1 920.61,-957.1"/>
<text text-anchor="middle" x="1077.12" y="-1132.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Abstract&gt;&gt;</text>
<text text-anchor="middle" x="1077.12" y="-1115.7" font-family="Times,serif" font-size="14.00">OwnableStorage</text>
<text text-anchor="middle" x="1077.12" y="-1098.9" font-family="Times,serif" font-size="14.00">public/flatten/RBACModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="920.61,-1090.7 1233.64,-1090.7 "/>
<text text-anchor="start" x="928.61" y="-1074.1" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="928.61" y="-1057.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;_SLOT_OWNABLE_STORAGE: bytes32</text>
<polyline fill="none" stroke="black" points="920.61,-1049.1 1233.64,-1049.1 "/>
<text text-anchor="start" x="928.61" y="-1032.5" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="928.61" y="-1015.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_checkOwner()</text>
<text text-anchor="start" x="928.61" y="-998.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_owner(): address</text>
<text text-anchor="start" x="928.61" y="-982.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_pendingOwner(): address</text>
<text text-anchor="start" x="928.61" y="-965.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_getOwnableStorage(): ($: Ownable2StepStorage)</text>
</g>
<!-- 3 -->
<g id="node4" class="node">
<title>3</title>
<polygon fill="#f2f2f2" stroke="black" points="959.47,-1186.1 959.47,-1286.1 1194.77,-1286.1 1194.77,-1186.1 959.47,-1186.1"/>
<text text-anchor="middle" x="1077.12" y="-1269.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="1077.12" y="-1252.7" font-family="Times,serif" font-size="14.00">Ownable2StepStorage</text>
<text text-anchor="middle" x="1077.12" y="-1235.9" font-family="Times,serif" font-size="14.00">public/flatten/RBACModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="959.47,-1227.7 1194.77,-1227.7 "/>
<text text-anchor="start" x="967.47" y="-1211.1" font-family="Times,serif" font-size="14.00">owner: address</text>
<text text-anchor="start" x="967.47" y="-1194.3" font-family="Times,serif" font-size="14.00">pendingOwner: address</text>
</g>
<!-- 2&#45;&gt;3 -->
<g id="edge2" class="edge">
<title>2&#45;&gt;3</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1070.38,-1149.11C1070.41,-1158.09 1070.53,-1167.01 1070.74,-1175.51"/>
<polygon fill="black" stroke="black" points="1067.24,-1175.77 1071.03,-1185.66 1074.24,-1175.56 1067.24,-1175.77"/>
</g>
<!-- 3&#45;&gt;2 -->
<g id="edge1" class="edge">
<title>3&#45;&gt;2</title>
<path fill="none" stroke="black" d="M1083.21,-1185.66C1083.48,-1177.91 1083.67,-1169.64 1083.77,-1161.18"/>
<polygon fill="black" stroke="black" points="1083.77,-1161.11 1079.82,-1155.07 1083.87,-1149.11 1087.82,-1155.14 1083.77,-1161.11"/>
</g>
<!-- 4 -->
<g id="node5" class="node">
<title>4</title>
<polygon fill="#f2f2f2" stroke="black" points="407.76,-560.1 407.76,-920.1 926.48,-920.1 926.48,-560.1 407.76,-560.1"/>
<text text-anchor="middle" x="667.12" y="-903.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Abstract&gt;&gt;</text>
<text text-anchor="middle" x="667.12" y="-886.7" font-family="Times,serif" font-size="14.00">RBACStorage</text>
<text text-anchor="middle" x="667.12" y="-869.9" font-family="Times,serif" font-size="14.00">public/flatten/RBACModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="407.76,-861.7 926.48,-861.7 "/>
<text text-anchor="start" x="415.76" y="-845.1" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="415.76" y="-828.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;_SLOT_RBAC_STORAGE: bytes32</text>
<text text-anchor="start" x="415.76" y="-811.5" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="415.76" y="-794.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;_CONFIGURATOR_PERMISSION: bytes32</text>
<text text-anchor="start" x="415.76" y="-777.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;_PROTOCOL_OWNER_PERMISSION: bytes32</text>
<text text-anchor="start" x="415.76" y="-761.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;_OWNER_WITHDRAWER_PERMISSION: bytes32</text>
<text text-anchor="start" x="415.76" y="-744.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;_PAUSE_GUARDIAN_PERMISSION: bytes32</text>
<text text-anchor="start" x="415.76" y="-727.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;_BORROW_CAP_GUARDIAN_PERMISSION: bytes32</text>
<text text-anchor="start" x="415.76" y="-710.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;_SUPPLY_CAP_GUARDIAN_PERMISSION: bytes32</text>
<text text-anchor="start" x="415.76" y="-693.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;_RESERVE_MANAGER_PERMISSION: bytes32</text>
<text text-anchor="start" x="415.76" y="-677.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;_RESERVE_WITHDRAWER_PERMISSION: bytes32</text>
<text text-anchor="start" x="415.76" y="-660.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;_EMERGENCY_WITHDRAWER_PERMISSION: bytes32</text>
<polyline fill="none" stroke="black" points="407.76,-652.1 926.48,-652.1 "/>
<text text-anchor="start" x="415.76" y="-635.5" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="415.76" y="-618.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_checkPermission(permission: bytes32, target: address)</text>
<text text-anchor="start" x="415.76" y="-601.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_checkNestedPermission(permission: bytes32, nestedAddress: address, target: address)</text>
<text text-anchor="start" x="415.76" y="-585.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_isPermissionValid(permission: bytes32)</text>
<text text-anchor="start" x="415.76" y="-568.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_getRBACStorage(): ($: RBACData)</text>
</g>
<!-- 5 -->
<g id="node6" class="node">
<title>5</title>
<polygon fill="#f2f2f2" stroke="black" points="401.34,-1003.1 401.34,-1103.1 902.91,-1103.1 902.91,-1003.1 401.34,-1003.1"/>
<text text-anchor="middle" x="652.12" y="-1086.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="652.12" y="-1069.7" font-family="Times,serif" font-size="14.00">RBACData</text>
<text text-anchor="middle" x="652.12" y="-1052.9" font-family="Times,serif" font-size="14.00">public/flatten/RBACModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="401.34,-1044.7 902.91,-1044.7 "/>
<text text-anchor="start" x="409.34" y="-1028.1" font-family="Times,serif" font-size="14.00">permissions: mapping(bytes32=&gt;mapping(address=&gt;bool))</text>
<text text-anchor="start" x="409.34" y="-1011.3" font-family="Times,serif" font-size="14.00">nestedPermissions: mapping(bytes32=&gt;mapping(address=&gt;mapping(address=&gt;bool)))</text>
</g>
<!-- 4&#45;&gt;5 -->
<g id="edge4" class="edge">
<title>4&#45;&gt;5</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M651.8,-920.21C650.7,-946.32 649.9,-971.52 649.55,-992.77"/>
<polygon fill="black" stroke="black" points="646.05,-992.83 649.41,-1002.87 653.04,-992.92 646.05,-992.83"/>
</g>
<!-- 5&#45;&gt;4 -->
<g id="edge3" class="edge">
<title>5&#45;&gt;4</title>
<path fill="none" stroke="black" d="M659.58,-1002.87C661.32,-982.81 663,-958.23 664.49,-932.24"/>
<polygon fill="black" stroke="black" points="664.5,-932.19 660.84,-925.98 665.16,-920.21 668.82,-926.42 664.5,-932.19"/>
</g>
<!-- 6 -->
<g id="node7" class="node">
<title>6</title>
<polygon fill="#f2f2f2" stroke="black" points="951.47,-698.5 951.47,-781.7 1186.77,-781.7 1186.77,-698.5 951.47,-698.5"/>
<text text-anchor="middle" x="1069.12" y="-765.1" font-family="Times,serif" font-size="14.00">OwnableMixin</text>
<text text-anchor="middle" x="1069.12" y="-748.3" font-family="Times,serif" font-size="14.00">public/flatten/RBACModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="951.47,-740.1 1186.77,-740.1 "/>
<text text-anchor="start" x="959.47" y="-723.5" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="959.47" y="-706.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;modifier&gt;&gt; onlyOwner()</text>
</g>
<!-- 6&#45;&gt;2 -->
<g id="edge5" class="edge">
<title>6&#45;&gt;2</title>
<path fill="none" stroke="black" d="M1070.17,-781.76C1071.12,-818.63 1072.57,-874.96 1073.9,-926.67"/>
<polygon fill="none" stroke="black" points="1063.4,-927.1 1074.67,-956.82 1084.4,-926.56 1063.4,-927.1"/>
</g>
<!-- 7 -->
<g id="node8" class="node">
<title>7</title>
<polygon fill="#f2f2f2" stroke="black" points="612.26,-330.7 612.26,-447.5 1123.98,-447.5 1123.98,-330.7 612.26,-330.7"/>
<text text-anchor="middle" x="868.12" y="-430.9" font-family="Times,serif" font-size="14.00">RBACMixin</text>
<text text-anchor="middle" x="868.12" y="-414.1" font-family="Times,serif" font-size="14.00">public/flatten/RBACModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="612.26,-405.9 1123.98,-405.9 "/>
<text text-anchor="start" x="620.26" y="-389.3" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="620.26" y="-372.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;checkPermissionOrAdmin(permission: bytes32, target: address)</text>
<text text-anchor="start" x="620.26" y="-355.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;checkPermission(permission: bytes32, target: address)</text>
<text text-anchor="start" x="620.26" y="-338.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;checkNestedPermission(permission: bytes32, nestedAddress: address, target: address)</text>
</g>
<!-- 7&#45;&gt;4 -->
<g id="edge6" class="edge">
<title>7&#45;&gt;4</title>
<path fill="none" stroke="black" d="M834.93,-447.73C820.84,-472.2 803.47,-502.35 785.31,-533.89"/>
<polygon fill="none" stroke="black" points="776.15,-528.75 770.28,-559.99 794.35,-539.24 776.15,-528.75"/>
</g>
<!-- 7&#45;&gt;6 -->
<g id="edge7" class="edge">
<title>7&#45;&gt;6</title>
<path fill="none" stroke="black" d="M901.31,-447.73C936.85,-509.42 993.15,-607.19 1030.61,-672.23"/>
<polygon fill="none" stroke="black" points="1021.56,-677.56 1045.63,-698.31 1039.76,-667.08 1021.56,-677.56"/>
</g>
<!-- 8 -->
<g id="node9" class="node">
<title>8</title>
<polygon fill="#f2f2f2" stroke="black" points="274.5,-0.5 274.5,-218.1 889.74,-218.1 889.74,-0.5 274.5,-0.5"/>
<text text-anchor="middle" x="582.12" y="-201.5" font-family="Times,serif" font-size="14.00">RBACModule</text>
<text text-anchor="middle" x="582.12" y="-184.7" font-family="Times,serif" font-size="14.00">public/flatten/RBACModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="274.5,-176.5 889.74,-176.5 "/>
<text text-anchor="start" x="282.5" y="-159.9" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="282.5" y="-143.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_hasPermission(permission: bytes32, target: address): bool</text>
<text text-anchor="start" x="282.5" y="-126.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_hasNestedPermission(permission: bytes32, nestedAddress: address, target: address): bool</text>
<text text-anchor="start" x="282.5" y="-109.5" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="282.5" y="-92.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;grantPermission(permission: bytes32, target: address) &lt;&lt;onlyOwner&gt;&gt;</text>
<text text-anchor="start" x="282.5" y="-75.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;grantNestedPermission(permission: bytes32, nestedAddress: address, target: address) &lt;&lt;onlyOwner&gt;&gt;</text>
<text text-anchor="start" x="282.5" y="-59.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;revokePermission(permission: bytes32, target: address) &lt;&lt;onlyOwner&gt;&gt;</text>
<text text-anchor="start" x="282.5" y="-42.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;revokeNestedPermission(permission: bytes32, nestedAddress: address, target: address) &lt;&lt;onlyOwner&gt;&gt;</text>
<text text-anchor="start" x="282.5" y="-25.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;hasPermission(permission: bytes32, target: address): bool</text>
<text text-anchor="start" x="282.5" y="-8.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;hasNestedPermission(permission: bytes32, nestedAddress: address, target: address): bool</text>
</g>
<!-- 8&#45;&gt;1 -->
<g id="edge8" class="edge">
<title>8&#45;&gt;1</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M471.53,-218.1C466.21,-223.28 460.84,-228.52 455.44,-233.79"/>
<polygon fill="none" stroke="black" points="447.98,-226.39 433.83,-254.85 462.64,-241.43 447.98,-226.39"/>
</g>
<!-- 8&#45;&gt;7 -->
<g id="edge9" class="edge">
<title>8&#45;&gt;7</title>
<path fill="none" stroke="black" d="M693.11,-218.1C724.36,-248.46 757.76,-280.9 786.92,-309.22"/>
<polygon fill="none" stroke="black" points="779.94,-317.08 808.78,-330.45 794.57,-302.02 779.94,-317.08"/>
</g>
</g>
</svg>
