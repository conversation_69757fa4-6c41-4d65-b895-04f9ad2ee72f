<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 5.0.0 (20220707.1540)
 -->
<!-- Title: UmlClassDiagram Pages: 1 -->
<svg width="6276pt" height="5394pt"
 viewBox="0.00 0.00 6276.27 5393.60" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 5389.6)">
<title>UmlClassDiagram</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-5389.6 6272.27,-5389.6 6272.27,4 -4,4"/>
<!-- 0 -->
<g id="node1" class="node">
<title>0</title>
<polygon fill="#f2f2f2" stroke="black" points="1281.08,-4384.7 1281.08,-4443.1 1543.59,-4443.1 1543.59,-4384.7 1281.08,-4384.7"/>
<text text-anchor="middle" x="1412.33" y="-4426.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="1412.33" y="-4409.7" font-family="Times,serif" font-size="14.00">IERC20Errors</text>
<text text-anchor="middle" x="1412.33" y="-4392.9" font-family="Times,serif" font-size="14.00">public/flatten/RiskEngineModule.flatten.sol</text>
</g>
<!-- 1 -->
<g id="node2" class="node">
<title>1</title>
<polygon fill="#f2f2f2" stroke="black" points="5075.08,-491.7 5075.08,-550.1 5337.59,-550.1 5337.59,-491.7 5075.08,-491.7"/>
<text text-anchor="middle" x="5206.33" y="-533.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="5206.33" y="-516.7" font-family="Times,serif" font-size="14.00">IERC721Errors</text>
<text text-anchor="middle" x="5206.33" y="-499.9" font-family="Times,serif" font-size="14.00">public/flatten/RiskEngineModule.flatten.sol</text>
</g>
<!-- 2 -->
<g id="node3" class="node">
<title>2</title>
<polygon fill="#f2f2f2" stroke="black" points="5356.08,-491.7 5356.08,-550.1 5618.59,-550.1 5618.59,-491.7 5356.08,-491.7"/>
<text text-anchor="middle" x="5487.33" y="-533.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="5487.33" y="-516.7" font-family="Times,serif" font-size="14.00">IERC1155Errors</text>
<text text-anchor="middle" x="5487.33" y="-499.9" font-family="Times,serif" font-size="14.00">public/flatten/RiskEngineModule.flatten.sol</text>
</g>
<!-- 3 -->
<g id="node4" class="node">
<title>3</title>
<polygon fill="#f2f2f2" stroke="black" points="1515.65,-5038.5 1515.65,-5272.9 1945.01,-5272.9 1945.01,-5038.5 1515.65,-5038.5"/>
<text text-anchor="middle" x="1730.33" y="-5256.3" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="1730.33" y="-5239.5" font-family="Times,serif" font-size="14.00">IERC20</text>
<text text-anchor="middle" x="1730.33" y="-5222.7" font-family="Times,serif" font-size="14.00">public/flatten/RiskEngineModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="1515.65,-5214.5 1945.01,-5214.5 "/>
<text text-anchor="start" x="1523.65" y="-5197.9" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="1523.65" y="-5181.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;totalSupply(): uint256</text>
<text text-anchor="start" x="1523.65" y="-5164.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;balanceOf(account: address): uint256</text>
<text text-anchor="start" x="1523.65" y="-5147.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;transfer(to: address, value: uint256): bool</text>
<text text-anchor="start" x="1523.65" y="-5130.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;allowance(owner: address, spender: address): uint256</text>
<text text-anchor="start" x="1523.65" y="-5113.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;approve(spender: address, value: uint256): bool</text>
<text text-anchor="start" x="1523.65" y="-5097.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;transferFrom(from: address, to: address, value: uint256): bool</text>
<text text-anchor="start" x="1523.65" y="-5080.3" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="1523.65" y="-5063.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; Transfer(from: address, to: address, value: uint256)</text>
<text text-anchor="start" x="1523.65" y="-5046.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; Approval(owner: address, spender: address, value: uint256)</text>
</g>
<!-- 4 -->
<g id="node5" class="node">
<title>4</title>
<polygon fill="#f2f2f2" stroke="black" points="5636.39,-454.1 5636.39,-587.7 6268.27,-587.7 6268.27,-454.1 5636.39,-454.1"/>
<text text-anchor="middle" x="5952.33" y="-571.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="5952.33" y="-554.3" font-family="Times,serif" font-size="14.00">IERC20Permit</text>
<text text-anchor="middle" x="5952.33" y="-537.5" font-family="Times,serif" font-size="14.00">public/flatten/RiskEngineModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="5636.39,-529.3 6268.27,-529.3 "/>
<text text-anchor="start" x="5644.39" y="-512.7" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="5644.39" y="-495.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;permit(owner: address, spender: address, value: uint256, deadline: uint256, v: uint8, r: bytes32, s: bytes32)</text>
<text text-anchor="start" x="5644.39" y="-479.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;nonces(owner: address): uint256</text>
<text text-anchor="start" x="5644.39" y="-462.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;DOMAIN_SEPARATOR(): bytes32</text>
</g>
<!-- 5 -->
<g id="node6" class="node">
<title>5</title>
<polygon fill="#f2f2f2" stroke="black" points="64.9,-4296.7 64.9,-4531.1 563.77,-4531.1 563.77,-4296.7 64.9,-4296.7"/>
<text text-anchor="middle" x="314.33" y="-4514.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Library&gt;&gt;</text>
<text text-anchor="middle" x="314.33" y="-4497.7" font-family="Times,serif" font-size="14.00">Address</text>
<text text-anchor="middle" x="314.33" y="-4480.9" font-family="Times,serif" font-size="14.00">public/flatten/RiskEngineModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="64.9,-4472.7 563.77,-4472.7 "/>
<text text-anchor="start" x="72.9" y="-4456.1" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="72.9" y="-4439.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_revert(returndata: bytes)</text>
<text text-anchor="start" x="72.9" y="-4422.5" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="72.9" y="-4405.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;sendValue(recipient: address, amount: uint256)</text>
<text text-anchor="start" x="72.9" y="-4388.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;functionCall(target: address, data: bytes): bytes</text>
<text text-anchor="start" x="72.9" y="-4372.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;functionCallWithValue(target: address, data: bytes, value: uint256): bytes</text>
<text text-anchor="start" x="72.9" y="-4355.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;functionStaticCall(target: address, data: bytes): bytes</text>
<text text-anchor="start" x="72.9" y="-4338.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;functionDelegateCall(target: address, data: bytes): bytes</text>
<text text-anchor="start" x="72.9" y="-4321.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;verifyCallResultFromTarget(target: address, success: bool, returndata: bytes): bytes</text>
<text text-anchor="start" x="72.9" y="-4304.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;verifyCallResult(success: bool, returndata: bytes): bytes</text>
</g>
<!-- 6 -->
<g id="node7" class="node">
<title>6</title>
<polygon fill="#f2f2f2" stroke="black" points="1000.08,-4347.1 1000.08,-4480.7 1262.59,-4480.7 1262.59,-4347.1 1000.08,-4347.1"/>
<text text-anchor="middle" x="1131.33" y="-4464.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Abstract&gt;&gt;</text>
<text text-anchor="middle" x="1131.33" y="-4447.3" font-family="Times,serif" font-size="14.00">Context</text>
<text text-anchor="middle" x="1131.33" y="-4430.5" font-family="Times,serif" font-size="14.00">public/flatten/RiskEngineModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="1000.08,-4422.3 1262.59,-4422.3 "/>
<text text-anchor="start" x="1008.08" y="-4405.7" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="1008.08" y="-4388.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_msgSender(): address</text>
<text text-anchor="start" x="1008.08" y="-4372.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_msgData(): bytes</text>
<text text-anchor="start" x="1008.08" y="-4355.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_contextSuffixLength(): uint256</text>
</g>
<!-- 7 -->
<g id="node8" class="node">
<title>7</title>
<polygon fill="#f2f2f2" stroke="black" points="630.48,-3349.3 630.48,-3768.5 1132.19,-3768.5 1132.19,-3349.3 630.48,-3349.3"/>
<text text-anchor="middle" x="881.33" y="-3751.9" font-family="Times,serif" font-size="14.00">&lt;&lt;Library&gt;&gt;</text>
<text text-anchor="middle" x="881.33" y="-3735.1" font-family="Times,serif" font-size="14.00">Math</text>
<text text-anchor="middle" x="881.33" y="-3718.3" font-family="Times,serif" font-size="14.00">public/flatten/RiskEngineModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="630.48,-3710.1 1132.19,-3710.1 "/>
<text text-anchor="start" x="638.48" y="-3693.5" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="638.48" y="-3676.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;tryAdd(a: uint256, b: uint256): (bool, uint256)</text>
<text text-anchor="start" x="638.48" y="-3659.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;trySub(a: uint256, b: uint256): (bool, uint256)</text>
<text text-anchor="start" x="638.48" y="-3643.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;tryMul(a: uint256, b: uint256): (bool, uint256)</text>
<text text-anchor="start" x="638.48" y="-3626.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;tryDiv(a: uint256, b: uint256): (bool, uint256)</text>
<text text-anchor="start" x="638.48" y="-3609.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;tryMod(a: uint256, b: uint256): (bool, uint256)</text>
<text text-anchor="start" x="638.48" y="-3592.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;max(a: uint256, b: uint256): uint256</text>
<text text-anchor="start" x="638.48" y="-3575.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;min(a: uint256, b: uint256): uint256</text>
<text text-anchor="start" x="638.48" y="-3559.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;average(a: uint256, b: uint256): uint256</text>
<text text-anchor="start" x="638.48" y="-3542.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;ceilDiv(a: uint256, b: uint256): uint256</text>
<text text-anchor="start" x="638.48" y="-3525.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;mulDiv(x: uint256, y: uint256, denominator: uint256): (result: uint256)</text>
<text text-anchor="start" x="638.48" y="-3508.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;mulDiv(x: uint256, y: uint256, denominator: uint256, rounding: Rounding): uint256</text>
<text text-anchor="start" x="638.48" y="-3491.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;sqrt(a: uint256): uint256</text>
<text text-anchor="start" x="638.48" y="-3475.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;sqrt(a: uint256, rounding: Rounding): uint256</text>
<text text-anchor="start" x="638.48" y="-3458.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;log2(value: uint256): uint256</text>
<text text-anchor="start" x="638.48" y="-3441.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;log2(value: uint256, rounding: Rounding): uint256</text>
<text text-anchor="start" x="638.48" y="-3424.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;log10(value: uint256): uint256</text>
<text text-anchor="start" x="638.48" y="-3407.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;log10(value: uint256, rounding: Rounding): uint256</text>
<text text-anchor="start" x="638.48" y="-3391.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;log256(value: uint256): uint256</text>
<text text-anchor="start" x="638.48" y="-3374.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;log256(value: uint256, rounding: Rounding): uint256</text>
<text text-anchor="start" x="638.48" y="-3357.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;unsignedRoundsUp(rounding: Rounding): bool</text>
</g>
<!-- 8 -->
<g id="node9" class="node">
<title>8</title>
<polygon fill="#f2f2f2" stroke="black" points="719.08,-4347.1 719.08,-4480.7 981.59,-4480.7 981.59,-4347.1 719.08,-4347.1"/>
<text text-anchor="middle" x="850.33" y="-4464.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Enum&gt;&gt;</text>
<text text-anchor="middle" x="850.33" y="-4447.3" font-family="Times,serif" font-size="14.00">Rounding</text>
<text text-anchor="middle" x="850.33" y="-4430.5" font-family="Times,serif" font-size="14.00">public/flatten/RiskEngineModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="719.08,-4422.3 981.59,-4422.3 "/>
<text text-anchor="start" x="727.08" y="-4405.7" font-family="Times,serif" font-size="14.00">Floor: 0</text>
<text text-anchor="start" x="727.08" y="-4388.9" font-family="Times,serif" font-size="14.00">Ceil: 1</text>
<text text-anchor="start" x="727.08" y="-4372.1" font-family="Times,serif" font-size="14.00">Trunc: 2</text>
<text text-anchor="start" x="727.08" y="-4355.3" font-family="Times,serif" font-size="14.00">Expand: 3</text>
</g>
<!-- 7&#45;&gt;8 -->
<g id="edge2" class="edge">
<title>7&#45;&gt;8</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M867.85,-3768.57C859.6,-3952.31 850.85,-4209.77 849.05,-4336.41"/>
<polygon fill="black" stroke="black" points="845.54,-4336.73 848.91,-4346.78 852.54,-4336.83 845.54,-4336.73"/>
</g>
<!-- 8&#45;&gt;7 -->
<g id="edge1" class="edge">
<title>8&#45;&gt;7</title>
<path fill="none" stroke="black" d="M856.56,-4346.78C863.86,-4226.88 873.97,-3968.61 879.31,-3780.57"/>
<polygon fill="black" stroke="black" points="879.31,-3780.56 875.48,-3774.45 879.65,-3768.57 883.48,-3774.68 879.31,-3780.56"/>
</g>
<!-- 9 -->
<g id="node10" class="node">
<title>9</title>
<polygon fill="#f2f2f2" stroke="black" points="2026.08,-1191.5 2026.08,-1249.9 2288.59,-1249.9 2288.59,-1191.5 2026.08,-1191.5"/>
<text text-anchor="middle" x="2157.33" y="-1233.3" font-family="Times,serif" font-size="14.00">&lt;&lt;Library&gt;&gt;</text>
<text text-anchor="middle" x="2157.33" y="-1216.5" font-family="Times,serif" font-size="14.00">CommonError</text>
<text text-anchor="middle" x="2157.33" y="-1199.7" font-family="Times,serif" font-size="14.00">public/flatten/RiskEngineModule.flatten.sol</text>
</g>
<!-- 10 -->
<g id="node11" class="node">
<title>10</title>
<polygon fill="#f2f2f2" stroke="black" points="3260.08,-5326.7 3260.08,-5385.1 3522.59,-5385.1 3522.59,-5326.7 3260.08,-5326.7"/>
<text text-anchor="middle" x="3391.33" y="-5368.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Library&gt;&gt;</text>
<text text-anchor="middle" x="3391.33" y="-5351.7" font-family="Times,serif" font-size="14.00">RiskEngineError</text>
<text text-anchor="middle" x="3391.33" y="-5334.9" font-family="Times,serif" font-size="14.00">public/flatten/RiskEngineModule.flatten.sol</text>
</g>
<!-- 11 -->
<g id="node12" class="node">
<title>11</title>
<polygon fill="#f2f2f2" stroke="black" points="3260.08,-5021.7 3260.08,-5289.7 3522.59,-5289.7 3522.59,-5021.7 3260.08,-5021.7"/>
<text text-anchor="middle" x="3391.33" y="-5273.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Enum&gt;&gt;</text>
<text text-anchor="middle" x="3391.33" y="-5256.3" font-family="Times,serif" font-size="14.00">Error</text>
<text text-anchor="middle" x="3391.33" y="-5239.5" font-family="Times,serif" font-size="14.00">public/flatten/RiskEngineModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="3260.08,-5231.3 3522.59,-5231.3 "/>
<text text-anchor="start" x="3268.08" y="-5214.7" font-family="Times,serif" font-size="14.00">NO_ERROR: 0</text>
<text text-anchor="start" x="3268.08" y="-5197.9" font-family="Times,serif" font-size="14.00">RISKENGINE_MISMATCH: 1</text>
<text text-anchor="start" x="3268.08" y="-5181.1" font-family="Times,serif" font-size="14.00">INSUFFICIENT_SHORTFALL: 2</text>
<text text-anchor="start" x="3268.08" y="-5164.3" font-family="Times,serif" font-size="14.00">INSUFFICIENT_LIQUIDITY: 3</text>
<text text-anchor="start" x="3268.08" y="-5147.5" font-family="Times,serif" font-size="14.00">MARKET_NOT_LISTED: 4</text>
<text text-anchor="start" x="3268.08" y="-5130.7" font-family="Times,serif" font-size="14.00">PRICE_ERROR: 5</text>
<text text-anchor="start" x="3268.08" y="-5113.9" font-family="Times,serif" font-size="14.00">TOO_MUCH_REPAY: 6</text>
<text text-anchor="start" x="3268.08" y="-5097.1" font-family="Times,serif" font-size="14.00">SUPPLY_CAP_EXCEEDED: 7</text>
<text text-anchor="start" x="3268.08" y="-5080.3" font-family="Times,serif" font-size="14.00">BORROW_CAP_EXCEEDED: 8</text>
<text text-anchor="start" x="3268.08" y="-5063.5" font-family="Times,serif" font-size="14.00">NOT_ALLOWED_AS_COLLATERAL: 9</text>
<text text-anchor="start" x="3268.08" y="-5046.7" font-family="Times,serif" font-size="14.00">NOT_ALLOWED_TO_BORROW: 10</text>
<text text-anchor="start" x="3268.08" y="-5029.9" font-family="Times,serif" font-size="14.00">EMODE_NOT_ALLOWED: 11</text>
</g>
<!-- 11&#45;&gt;10 -->
<g id="edge3" class="edge">
<title>11&#45;&gt;10</title>
<path fill="none" stroke="black" d="M3391.33,-5289.73C3391.33,-5298.46 3391.33,-5306.79 3391.33,-5314.41"/>
<polygon fill="black" stroke="black" points="3391.33,-5314.48 3395.33,-5320.48 3391.33,-5326.48 3387.33,-5320.48 3391.33,-5314.48"/>
</g>
<!-- 12 -->
<g id="node13" class="node">
<title>12</title>
<polygon fill="#f2f2f2" stroke="black" points="4046.82,-2557.5 4046.82,-2749.5 4359.85,-2749.5 4359.85,-2557.5 4046.82,-2557.5"/>
<text text-anchor="middle" x="4203.33" y="-2732.9" font-family="Times,serif" font-size="14.00">&lt;&lt;Abstract&gt;&gt;</text>
<text text-anchor="middle" x="4203.33" y="-2716.1" font-family="Times,serif" font-size="14.00">OwnableStorage</text>
<text text-anchor="middle" x="4203.33" y="-2699.3" font-family="Times,serif" font-size="14.00">public/flatten/RiskEngineModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="4046.82,-2691.1 4359.85,-2691.1 "/>
<text text-anchor="start" x="4054.82" y="-2674.5" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="4054.82" y="-2657.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;_SLOT_OWNABLE_STORAGE: bytes32</text>
<polyline fill="none" stroke="black" points="4046.82,-2649.5 4359.85,-2649.5 "/>
<text text-anchor="start" x="4054.82" y="-2632.9" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="4054.82" y="-2616.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_checkOwner()</text>
<text text-anchor="start" x="4054.82" y="-2599.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_owner(): address</text>
<text text-anchor="start" x="4054.82" y="-2582.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_pendingOwner(): address</text>
<text text-anchor="start" x="4054.82" y="-2565.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_getOwnableStorage(): ($: Ownable2StepStorage)</text>
</g>
<!-- 13 -->
<g id="node14" class="node">
<title>13</title>
<polygon fill="#f2f2f2" stroke="black" points="4072.08,-3508.9 4072.08,-3608.9 4334.59,-3608.9 4334.59,-3508.9 4072.08,-3508.9"/>
<text text-anchor="middle" x="4203.33" y="-3592.3" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="4203.33" y="-3575.5" font-family="Times,serif" font-size="14.00">Ownable2StepStorage</text>
<text text-anchor="middle" x="4203.33" y="-3558.7" font-family="Times,serif" font-size="14.00">public/flatten/RiskEngineModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="4072.08,-3550.5 4334.59,-3550.5 "/>
<text text-anchor="start" x="4080.08" y="-3533.9" font-family="Times,serif" font-size="14.00">owner: address</text>
<text text-anchor="start" x="4080.08" y="-3517.1" font-family="Times,serif" font-size="14.00">pendingOwner: address</text>
</g>
<!-- 12&#45;&gt;13 -->
<g id="edge5" class="edge">
<title>12&#45;&gt;13</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4198.99,-2749.9C4195.54,-2936.67 4195.8,-3345.67 4199.77,-3498.62"/>
<polygon fill="black" stroke="black" points="4196.28,-3498.85 4200.05,-3508.75 4203.28,-3498.66 4196.28,-3498.85"/>
</g>
<!-- 13&#45;&gt;12 -->
<g id="edge4" class="edge">
<title>13&#45;&gt;12</title>
<path fill="none" stroke="black" d="M4206.61,-3508.75C4210.76,-3367.54 4211.19,-2958.22 4207.89,-2761.94"/>
<polygon fill="black" stroke="black" points="4207.89,-2761.9 4203.79,-2755.97 4207.68,-2749.9 4211.78,-2755.83 4207.89,-2761.9"/>
</g>
<!-- 14 -->
<g id="node15" class="node">
<title>14</title>
<polygon fill="#f2f2f2" stroke="black" points="2381.97,-1517.7 2381.97,-1877.7 2900.69,-1877.7 2900.69,-1517.7 2381.97,-1517.7"/>
<text text-anchor="middle" x="2641.33" y="-1861.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Abstract&gt;&gt;</text>
<text text-anchor="middle" x="2641.33" y="-1844.3" font-family="Times,serif" font-size="14.00">RBACStorage</text>
<text text-anchor="middle" x="2641.33" y="-1827.5" font-family="Times,serif" font-size="14.00">public/flatten/RiskEngineModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="2381.97,-1819.3 2900.69,-1819.3 "/>
<text text-anchor="start" x="2389.97" y="-1802.7" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="2389.97" y="-1785.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;_SLOT_RBAC_STORAGE: bytes32</text>
<text text-anchor="start" x="2389.97" y="-1769.1" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="2389.97" y="-1752.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;_CONFIGURATOR_PERMISSION: bytes32</text>
<text text-anchor="start" x="2389.97" y="-1735.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;_PROTOCOL_OWNER_PERMISSION: bytes32</text>
<text text-anchor="start" x="2389.97" y="-1718.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;_OWNER_WITHDRAWER_PERMISSION: bytes32</text>
<text text-anchor="start" x="2389.97" y="-1701.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;_PAUSE_GUARDIAN_PERMISSION: bytes32</text>
<text text-anchor="start" x="2389.97" y="-1685.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;_BORROW_CAP_GUARDIAN_PERMISSION: bytes32</text>
<text text-anchor="start" x="2389.97" y="-1668.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;_SUPPLY_CAP_GUARDIAN_PERMISSION: bytes32</text>
<text text-anchor="start" x="2389.97" y="-1651.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;_RESERVE_MANAGER_PERMISSION: bytes32</text>
<text text-anchor="start" x="2389.97" y="-1634.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;_RESERVE_WITHDRAWER_PERMISSION: bytes32</text>
<text text-anchor="start" x="2389.97" y="-1617.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;_EMERGENCY_WITHDRAWER_PERMISSION: bytes32</text>
<polyline fill="none" stroke="black" points="2381.97,-1609.7 2900.69,-1609.7 "/>
<text text-anchor="start" x="2389.97" y="-1593.1" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="2389.97" y="-1576.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_checkPermission(permission: bytes32, target: address)</text>
<text text-anchor="start" x="2389.97" y="-1559.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_checkNestedPermission(permission: bytes32, nestedAddress: address, target: address)</text>
<text text-anchor="start" x="2389.97" y="-1542.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_isPermissionValid(permission: bytes32)</text>
<text text-anchor="start" x="2389.97" y="-1525.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_getRBACStorage(): ($: RBACData)</text>
</g>
<!-- 15 -->
<g id="node16" class="node">
<title>15</title>
<polygon fill="#f2f2f2" stroke="black" points="2382.55,-2603.5 2382.55,-2703.5 2884.12,-2703.5 2884.12,-2603.5 2382.55,-2603.5"/>
<text text-anchor="middle" x="2633.33" y="-2686.9" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="2633.33" y="-2670.1" font-family="Times,serif" font-size="14.00">RBACData</text>
<text text-anchor="middle" x="2633.33" y="-2653.3" font-family="Times,serif" font-size="14.00">public/flatten/RiskEngineModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="2382.55,-2645.1 2884.12,-2645.1 "/>
<text text-anchor="start" x="2390.55" y="-2628.5" font-family="Times,serif" font-size="14.00">permissions: mapping(bytes32=&gt;mapping(address=&gt;bool))</text>
<text text-anchor="start" x="2390.55" y="-2611.7" font-family="Times,serif" font-size="14.00">nestedPermissions: mapping(bytes32=&gt;mapping(address=&gt;mapping(address=&gt;bool)))</text>
</g>
<!-- 14&#45;&gt;15 -->
<g id="edge7" class="edge">
<title>14&#45;&gt;15</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2634.42,-1877.82C2630.3,-2096 2627.95,-2452.78 2630.35,-2593.13"/>
<polygon fill="black" stroke="black" points="2626.85,-2593.32 2630.54,-2603.25 2633.85,-2593.19 2626.85,-2593.32"/>
</g>
<!-- 15&#45;&gt;14 -->
<g id="edge6" class="edge">
<title>15&#45;&gt;14</title>
<path fill="none" stroke="black" d="M2636.95,-2603.25C2641.79,-2473.12 2645.57,-2114.72 2645.26,-1890.08"/>
<polygon fill="black" stroke="black" points="2645.26,-1889.82 2641.25,-1883.83 2645.24,-1877.82 2649.25,-1883.82 2645.26,-1889.82"/>
</g>
<!-- 16 -->
<g id="node17" class="node">
<title>16</title>
<polygon fill="#f2f2f2" stroke="black" points="4098.12,-1400.1 4098.12,-1995.3 4598.55,-1995.3 4598.55,-1400.1 4098.12,-1400.1"/>
<text text-anchor="middle" x="4348.33" y="-1978.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Library&gt;&gt;</text>
<text text-anchor="middle" x="4348.33" y="-1961.9" font-family="Times,serif" font-size="14.00">ExponentialNoError</text>
<text text-anchor="middle" x="4348.33" y="-1945.1" font-family="Times,serif" font-size="14.00">public/flatten/RiskEngineModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="4098.12,-1936.9 4598.55,-1936.9 "/>
<text text-anchor="start" x="4106.12" y="-1920.3" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="4106.12" y="-1903.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;expScale: uint256</text>
<text text-anchor="start" x="4106.12" y="-1886.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;doubleScale: uint256</text>
<polyline fill="none" stroke="black" points="4098.12,-1878.5 4598.55,-1878.5 "/>
<text text-anchor="start" x="4106.12" y="-1861.9" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="4106.12" y="-1845.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;toExp(value: uint256): Exp</text>
<text text-anchor="start" x="4106.12" y="-1828.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;toDouble(value: uint256): Double</text>
<text text-anchor="start" x="4106.12" y="-1811.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;truncate(value: Exp): uint256</text>
<text text-anchor="start" x="4106.12" y="-1794.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;mul_ScalarTruncate(value: Exp, scalar: uint256): uint256</text>
<text text-anchor="start" x="4106.12" y="-1777.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;mul_ScalarTruncateAddUInt(value: Exp, scalar: uint256, addend: uint256): uint256</text>
<text text-anchor="start" x="4106.12" y="-1761.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;lessThanExp(left: Exp, right: Exp): bool</text>
<text text-anchor="start" x="4106.12" y="-1744.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;lessThanOrEqualExp(left: Exp, right: Exp): bool</text>
<text text-anchor="start" x="4106.12" y="-1727.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;add_(a: Exp, b: Exp): Exp</text>
<text text-anchor="start" x="4106.12" y="-1710.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;add_(a: Double, b: Double): Double</text>
<text text-anchor="start" x="4106.12" y="-1693.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;add_(a: uint256, b: uint256): uint256</text>
<text text-anchor="start" x="4106.12" y="-1677.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;sub_(a: Exp, b: Exp): Exp</text>
<text text-anchor="start" x="4106.12" y="-1660.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;sub_(a: Double, b: Double): Double</text>
<text text-anchor="start" x="4106.12" y="-1643.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;sub_(a: uint256, b: uint256): uint256</text>
<text text-anchor="start" x="4106.12" y="-1626.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;mul_(a: Exp, b: Exp): Exp</text>
<text text-anchor="start" x="4106.12" y="-1609.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;mul_(a: Exp, b: uint256): Exp</text>
<text text-anchor="start" x="4106.12" y="-1593.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;mul_(a: uint256, b: Exp): uint256</text>
<text text-anchor="start" x="4106.12" y="-1576.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;mul_(a: Double, b: Double): Double</text>
<text text-anchor="start" x="4106.12" y="-1559.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;mul_(a: Double, b: uint256): Double</text>
<text text-anchor="start" x="4106.12" y="-1542.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;mul_(a: uint256, b: Double): uint256</text>
<text text-anchor="start" x="4106.12" y="-1525.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;mul_(a: uint256, b: uint256): uint256</text>
<text text-anchor="start" x="4106.12" y="-1509.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;div_(a: Exp, b: Exp): Exp</text>
<text text-anchor="start" x="4106.12" y="-1492.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;div_(a: Exp, b: uint256): Exp</text>
<text text-anchor="start" x="4106.12" y="-1475.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;div_(a: uint256, b: Exp): uint256</text>
<text text-anchor="start" x="4106.12" y="-1458.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;div_(a: Double, b: Double): Double</text>
<text text-anchor="start" x="4106.12" y="-1441.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;div_(a: Double, b: uint256): Double</text>
<text text-anchor="start" x="4106.12" y="-1425.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;div_(a: uint256, b: Double): uint256</text>
<text text-anchor="start" x="4106.12" y="-1408.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;div_(a: uint256, b: uint256): uint256</text>
</g>
<!-- 17 -->
<g id="node18" class="node">
<title>17</title>
<polygon fill="#f2f2f2" stroke="black" points="1599.08,-4347.1 1599.08,-4480.7 1861.59,-4480.7 1861.59,-4347.1 1599.08,-4347.1"/>
<text text-anchor="middle" x="1730.33" y="-4464.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="1730.33" y="-4447.3" font-family="Times,serif" font-size="14.00">IERC20Metadata</text>
<text text-anchor="middle" x="1730.33" y="-4430.5" font-family="Times,serif" font-size="14.00">public/flatten/RiskEngineModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="1599.08,-4422.3 1861.59,-4422.3 "/>
<text text-anchor="start" x="1607.08" y="-4405.7" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="1607.08" y="-4388.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;name(): string</text>
<text text-anchor="start" x="1607.08" y="-4372.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;symbol(): string</text>
<text text-anchor="start" x="1607.08" y="-4355.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;decimals(): uint8</text>
</g>
<!-- 17&#45;&gt;3 -->
<g id="edge8" class="edge">
<title>17&#45;&gt;3</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1730.33,-4480.88C1730.33,-4598.7 1730.33,-4847.9 1730.33,-5008"/>
<polygon fill="none" stroke="black" points="1719.83,-5008.28 1730.33,-5038.28 1740.83,-5008.28 1719.83,-5008.28"/>
</g>
<!-- 18 -->
<g id="node19" class="node">
<title>18</title>
<polygon fill="#f2f2f2" stroke="black" points="3818.08,-1656.1 3818.08,-1739.3 4080.59,-1739.3 4080.59,-1656.1 3818.08,-1656.1"/>
<text text-anchor="middle" x="3949.33" y="-1722.7" font-family="Times,serif" font-size="14.00">OwnableMixin</text>
<text text-anchor="middle" x="3949.33" y="-1705.9" font-family="Times,serif" font-size="14.00">public/flatten/RiskEngineModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="3818.08,-1697.7 4080.59,-1697.7 "/>
<text text-anchor="start" x="3826.08" y="-1681.1" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="3826.08" y="-1664.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;modifier&gt;&gt; onlyOwner()</text>
</g>
<!-- 18&#45;&gt;12 -->
<g id="edge9" class="edge">
<title>18&#45;&gt;12</title>
<path fill="none" stroke="black" d="M3960.17,-1739.38C3996.11,-1874.37 4112.45,-2311.21 4170.19,-2528.05"/>
<polygon fill="none" stroke="black" points="4160.07,-2530.84 4177.94,-2557.13 4180.36,-2525.44 4160.07,-2530.84"/>
</g>
<!-- 19 -->
<g id="node20" class="node">
<title>19</title>
<polygon fill="#f2f2f2" stroke="black" points="1666.63,-3357.7 1666.63,-3760.1 2288.04,-3760.1 2288.04,-3357.7 1666.63,-3357.7"/>
<text text-anchor="middle" x="1977.33" y="-3743.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="1977.33" y="-3726.7" font-family="Times,serif" font-size="14.00">IERC4626</text>
<text text-anchor="middle" x="1977.33" y="-3709.9" font-family="Times,serif" font-size="14.00">public/flatten/RiskEngineModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="1666.63,-3701.7 2288.04,-3701.7 "/>
<text text-anchor="start" x="1674.63" y="-3685.1" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="1674.63" y="-3668.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;asset(): (assetTokenAddress: address)</text>
<text text-anchor="start" x="1674.63" y="-3651.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;totalAssets(): (totalManagedAssets: uint256)</text>
<text text-anchor="start" x="1674.63" y="-3634.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;convertToShares(assets: uint256): (shares: uint256)</text>
<text text-anchor="start" x="1674.63" y="-3617.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;convertToAssets(shares: uint256): (assets: uint256)</text>
<text text-anchor="start" x="1674.63" y="-3601.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;maxDeposit(receiver: address): (maxAssets: uint256)</text>
<text text-anchor="start" x="1674.63" y="-3584.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;previewDeposit(assets: uint256): (shares: uint256)</text>
<text text-anchor="start" x="1674.63" y="-3567.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;deposit(assets: uint256, receiver: address): (shares: uint256)</text>
<text text-anchor="start" x="1674.63" y="-3550.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;maxMint(receiver: address): (maxShares: uint256)</text>
<text text-anchor="start" x="1674.63" y="-3533.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;previewMint(shares: uint256): (assets: uint256)</text>
<text text-anchor="start" x="1674.63" y="-3517.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;mint(shares: uint256, receiver: address): (assets: uint256)</text>
<text text-anchor="start" x="1674.63" y="-3500.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;maxWithdraw(owner: address): (maxAssets: uint256)</text>
<text text-anchor="start" x="1674.63" y="-3483.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;previewWithdraw(assets: uint256): (shares: uint256)</text>
<text text-anchor="start" x="1674.63" y="-3466.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;withdraw(assets: uint256, receiver: address, owner: address): (shares: uint256)</text>
<text text-anchor="start" x="1674.63" y="-3449.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;maxRedeem(owner: address): (maxShares: uint256)</text>
<text text-anchor="start" x="1674.63" y="-3433.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;previewRedeem(shares: uint256): (assets: uint256)</text>
<text text-anchor="start" x="1674.63" y="-3416.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;redeem(shares: uint256, receiver: address, owner: address): (assets: uint256)</text>
<text text-anchor="start" x="1674.63" y="-3399.5" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="1674.63" y="-3382.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; Deposit(sender: address, owner: address, assets: uint256, shares: uint256)</text>
<text text-anchor="start" x="1674.63" y="-3365.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; Withdraw(sender: address, receiver: address, owner: address, assets: uint256, shares: uint256)</text>
</g>
<!-- 19&#45;&gt;3 -->
<g id="edge10" class="edge">
<title>19&#45;&gt;3</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1999.37,-3760.31C2022.51,-4044.2 2035.6,-4574.58 1870.33,-4985.2 1866.57,-4994.55 1862.09,-5003.77 1857.09,-5012.8"/>
<polygon fill="none" stroke="black" points="1848.1,-5007.38 1841.23,-5038.41 1865.95,-5018.43 1848.1,-5007.38"/>
</g>
<!-- 19&#45;&gt;17 -->
<g id="edge11" class="edge">
<title>19&#45;&gt;17</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1919.34,-3760.16C1868.48,-3935.83 1796.74,-4183.56 1757.91,-4317.67"/>
<polygon fill="none" stroke="black" points="1747.72,-4315.11 1749.46,-4346.85 1767.89,-4320.96 1747.72,-4315.11"/>
</g>
<!-- 20 -->
<g id="node21" class="node">
<title>20</title>
<polygon fill="#f2f2f2" stroke="black" points="54.24,-3450.1 54.24,-3667.7 574.43,-3667.7 574.43,-3450.1 54.24,-3450.1"/>
<text text-anchor="middle" x="314.33" y="-3651.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Library&gt;&gt;</text>
<text text-anchor="middle" x="314.33" y="-3634.3" font-family="Times,serif" font-size="14.00">SafeERC20</text>
<text text-anchor="middle" x="314.33" y="-3617.5" font-family="Times,serif" font-size="14.00">public/flatten/RiskEngineModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="54.24,-3609.3 574.43,-3609.3 "/>
<text text-anchor="start" x="62.24" y="-3592.7" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="62.24" y="-3575.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_callOptionalReturn(token: IERC20, data: bytes)</text>
<text text-anchor="start" x="62.24" y="-3559.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_callOptionalReturnBool(token: IERC20, data: bytes): bool</text>
<text text-anchor="start" x="62.24" y="-3542.3" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="62.24" y="-3525.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;safeTransfer(token: IERC20, to: address, value: uint256)</text>
<text text-anchor="start" x="62.24" y="-3508.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;safeTransferFrom(token: IERC20, from: address, to: address, value: uint256)</text>
<text text-anchor="start" x="62.24" y="-3491.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;safeIncreaseAllowance(token: IERC20, spender: address, value: uint256)</text>
<text text-anchor="start" x="62.24" y="-3475.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;safeDecreaseAllowance(token: IERC20, spender: address, requestedDecrease: uint256)</text>
<text text-anchor="start" x="62.24" y="-3458.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;forceApprove(token: IERC20, spender: address, value: uint256)</text>
</g>
<!-- 20&#45;&gt;3 -->
<g id="edge13" class="edge">
<title>20&#45;&gt;3</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M442.32,-3667.7C491.12,-3716.02 542.21,-3776.92 572.33,-3842.6 678.94,-4075.08 533.55,-4800.38 710.33,-4985.2 816.76,-5096.47 1238.56,-5134.73 1505.29,-5147.86"/>
<polygon fill="black" stroke="black" points="1505.34,-5151.37 1515.5,-5148.35 1505.68,-5144.37 1505.34,-5151.37"/>
</g>
<!-- 20&#45;&gt;5 -->
<g id="edge12" class="edge">
<title>20&#45;&gt;5</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M314.33,-3667.93C314.33,-3825.76 314.33,-4118.77 314.33,-4286.43"/>
<polygon fill="black" stroke="black" points="310.83,-4286.55 314.33,-4296.55 317.83,-4286.55 310.83,-4286.55"/>
</g>
<!-- 21 -->
<g id="node22" class="node">
<title>21</title>
<polygon fill="#f2f2f2" stroke="black" points="3693.47,-1162.3 3693.47,-1279.1 4205.19,-1279.1 4205.19,-1162.3 3693.47,-1162.3"/>
<text text-anchor="middle" x="3949.33" y="-1262.5" font-family="Times,serif" font-size="14.00">RBACMixin</text>
<text text-anchor="middle" x="3949.33" y="-1245.7" font-family="Times,serif" font-size="14.00">public/flatten/RiskEngineModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="3693.47,-1237.5 4205.19,-1237.5 "/>
<text text-anchor="start" x="3701.47" y="-1220.9" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="3701.47" y="-1204.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;checkPermissionOrAdmin(permission: bytes32, target: address)</text>
<text text-anchor="start" x="3701.47" y="-1187.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;checkPermission(permission: bytes32, target: address)</text>
<text text-anchor="start" x="3701.47" y="-1170.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;checkNestedPermission(permission: bytes32, nestedAddress: address, target: address)</text>
</g>
<!-- 21&#45;&gt;14 -->
<g id="edge14" class="edge">
<title>21&#45;&gt;14</title>
<path fill="none" stroke="black" d="M3693.32,-1249.84C3440.72,-1280.67 3074.47,-1334.51 2947.33,-1399.6 2898.84,-1424.43 2852.94,-1459.52 2812.16,-1496.94"/>
<polygon fill="none" stroke="black" points="2804.91,-1489.34 2790.36,-1517.6 2819.36,-1504.58 2804.91,-1489.34"/>
</g>
<!-- 21&#45;&gt;18 -->
<g id="edge15" class="edge">
<title>21&#45;&gt;18</title>
<path fill="none" stroke="black" d="M3949.33,-1279.47C3949.33,-1366.61 3949.33,-1531.49 3949.33,-1625.78"/>
<polygon fill="none" stroke="black" points="3938.83,-1625.95 3949.33,-1655.95 3959.83,-1625.95 3938.83,-1625.95"/>
</g>
<!-- 22 -->
<g id="node23" class="node">
<title>22</title>
<polygon fill="#f2f2f2" stroke="black" points="1150.34,-3311.7 1150.34,-3806.1 1610.33,-3806.1 1610.33,-3311.7 1150.34,-3311.7"/>
<text text-anchor="middle" x="1380.33" y="-3789.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Abstract&gt;&gt;</text>
<text text-anchor="middle" x="1380.33" y="-3772.7" font-family="Times,serif" font-size="14.00">ERC20</text>
<text text-anchor="middle" x="1380.33" y="-3755.9" font-family="Times,serif" font-size="14.00">public/flatten/RiskEngineModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="1150.34,-3747.7 1610.33,-3747.7 "/>
<text text-anchor="start" x="1158.34" y="-3731.1" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="1158.34" y="-3714.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;_balances: mapping(address=&gt;uint256)</text>
<text text-anchor="start" x="1158.34" y="-3697.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;_allowances: mapping(address=&gt;mapping(address=&gt;uint256))</text>
<text text-anchor="start" x="1158.34" y="-3680.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;_totalSupply: uint256</text>
<text text-anchor="start" x="1158.34" y="-3663.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;_name: string</text>
<text text-anchor="start" x="1158.34" y="-3647.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;_symbol: string</text>
<polyline fill="none" stroke="black" points="1150.34,-3638.9 1610.33,-3638.9 "/>
<text text-anchor="start" x="1158.34" y="-3622.3" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="1158.34" y="-3605.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_transfer(from: address, to: address, value: uint256)</text>
<text text-anchor="start" x="1158.34" y="-3588.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_update(from: address, to: address, value: uint256)</text>
<text text-anchor="start" x="1158.34" y="-3571.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_mint(account: address, value: uint256)</text>
<text text-anchor="start" x="1158.34" y="-3555.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_burn(account: address, value: uint256)</text>
<text text-anchor="start" x="1158.34" y="-3538.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_approve(owner: address, spender: address, value: uint256)</text>
<text text-anchor="start" x="1158.34" y="-3521.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_approve(owner: address, spender: address, value: uint256, emitEvent: bool)</text>
<text text-anchor="start" x="1158.34" y="-3504.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_spendAllowance(owner: address, spender: address, value: uint256)</text>
<text text-anchor="start" x="1158.34" y="-3487.9" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="1158.34" y="-3471.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;constructor(name_: string, symbol_: string)</text>
<text text-anchor="start" x="1158.34" y="-3454.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;name(): string</text>
<text text-anchor="start" x="1158.34" y="-3437.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;symbol(): string</text>
<text text-anchor="start" x="1158.34" y="-3420.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;decimals(): uint8</text>
<text text-anchor="start" x="1158.34" y="-3403.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;totalSupply(): uint256</text>
<text text-anchor="start" x="1158.34" y="-3387.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;balanceOf(account: address): uint256</text>
<text text-anchor="start" x="1158.34" y="-3370.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;transfer(to: address, value: uint256): bool</text>
<text text-anchor="start" x="1158.34" y="-3353.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;allowance(owner: address, spender: address): uint256</text>
<text text-anchor="start" x="1158.34" y="-3336.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;approve(spender: address, value: uint256): bool</text>
<text text-anchor="start" x="1158.34" y="-3319.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;transferFrom(from: address, to: address, value: uint256): bool</text>
</g>
<!-- 22&#45;&gt;0 -->
<g id="edge19" class="edge">
<title>22&#45;&gt;0</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1389.58,-3806.34C1396.68,-3995.66 1405.96,-4242.96 1410.13,-4354.2"/>
<polygon fill="none" stroke="black" points="1399.65,-4354.84 1411.26,-4384.43 1420.63,-4354.05 1399.65,-4354.84"/>
</g>
<!-- 22&#45;&gt;3 -->
<g id="edge17" class="edge">
<title>22&#45;&gt;3</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1539.68,-3806.34C1544.46,-3818.37 1548.72,-3830.49 1552.33,-3842.6 1624.96,-4086.05 1500.41,-4747.6 1590.33,-4985.2 1593.81,-4994.39 1598.01,-5003.45 1602.74,-5012.31"/>
<polygon fill="none" stroke="black" points="1593.88,-5017.95 1618.37,-5038.21 1611.86,-5007.1 1593.88,-5017.95"/>
</g>
<!-- 22&#45;&gt;6 -->
<g id="edge16" class="edge">
<title>22&#45;&gt;6</title>
<path fill="none" stroke="black" d="M1284.16,-3806.35C1280.03,-3818.56 1276.06,-3830.69 1272.33,-3842.6 1220.78,-4007.13 1175.83,-4203.06 1151.24,-4317.17"/>
<polygon fill="none" stroke="black" points="1140.92,-4315.22 1144.93,-4346.75 1161.46,-4319.6 1140.92,-4315.22"/>
</g>
<!-- 22&#45;&gt;17 -->
<g id="edge18" class="edge">
<title>22&#45;&gt;17</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1566.87,-3806.42C1573.47,-3818.43 1579.67,-3830.52 1585.33,-3842.6 1659.19,-4000.21 1698.96,-4200.64 1717.31,-4317.08"/>
<polygon fill="none" stroke="black" points="1706.95,-4318.8 1721.82,-4346.89 1727.71,-4315.66 1706.95,-4318.8"/>
</g>
<!-- 23 -->
<g id="node24" class="node">
<title>23</title>
<polygon fill="#f2f2f2" stroke="black" points="1103.11,-2364.3 1103.11,-2942.7 1657.56,-2942.7 1657.56,-2364.3 1103.11,-2364.3"/>
<text text-anchor="middle" x="1380.33" y="-2926.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Abstract&gt;&gt;</text>
<text text-anchor="middle" x="1380.33" y="-2909.3" font-family="Times,serif" font-size="14.00">ERC4626</text>
<text text-anchor="middle" x="1380.33" y="-2892.5" font-family="Times,serif" font-size="14.00">public/flatten/RiskEngineModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="1103.11,-2884.3 1657.56,-2884.3 "/>
<text text-anchor="start" x="1111.11" y="-2867.7" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="1111.11" y="-2850.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;_asset: IERC20</text>
<text text-anchor="start" x="1111.11" y="-2834.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;_underlyingDecimals: uint8</text>
<polyline fill="none" stroke="black" points="1103.11,-2825.9 1657.56,-2825.9 "/>
<text text-anchor="start" x="1111.11" y="-2809.3" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="1111.11" y="-2792.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_tryGetAssetDecimals(asset_: IERC20): (bool, uint8)</text>
<text text-anchor="start" x="1111.11" y="-2775.7" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="1111.11" y="-2758.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_convertToShares(assets: uint256, rounding: Math.Rounding): uint256</text>
<text text-anchor="start" x="1111.11" y="-2742.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_convertToAssets(shares: uint256, rounding: Math.Rounding): uint256</text>
<text text-anchor="start" x="1111.11" y="-2725.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_deposit(caller: address, receiver: address, assets: uint256, shares: uint256)</text>
<text text-anchor="start" x="1111.11" y="-2708.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_withdraw(caller: address, receiver: address, owner: address, assets: uint256, shares: uint256)</text>
<text text-anchor="start" x="1111.11" y="-2691.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_decimalsOffset(): uint8</text>
<text text-anchor="start" x="1111.11" y="-2674.9" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="1111.11" y="-2658.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;constructor(asset_: IERC20)</text>
<text text-anchor="start" x="1111.11" y="-2641.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;decimals(): uint8</text>
<text text-anchor="start" x="1111.11" y="-2624.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;asset(): address</text>
<text text-anchor="start" x="1111.11" y="-2607.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;totalAssets(): uint256</text>
<text text-anchor="start" x="1111.11" y="-2590.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;convertToShares(assets: uint256): uint256</text>
<text text-anchor="start" x="1111.11" y="-2574.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;convertToAssets(shares: uint256): uint256</text>
<text text-anchor="start" x="1111.11" y="-2557.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;maxDeposit(address): uint256</text>
<text text-anchor="start" x="1111.11" y="-2540.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;maxMint(address): uint256</text>
<text text-anchor="start" x="1111.11" y="-2523.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;maxWithdraw(owner: address): uint256</text>
<text text-anchor="start" x="1111.11" y="-2506.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;maxRedeem(owner: address): uint256</text>
<text text-anchor="start" x="1111.11" y="-2490.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;previewDeposit(assets: uint256): uint256</text>
<text text-anchor="start" x="1111.11" y="-2473.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;previewMint(shares: uint256): uint256</text>
<text text-anchor="start" x="1111.11" y="-2456.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;previewWithdraw(assets: uint256): uint256</text>
<text text-anchor="start" x="1111.11" y="-2439.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;previewRedeem(shares: uint256): uint256</text>
<text text-anchor="start" x="1111.11" y="-2422.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;deposit(assets: uint256, receiver: address): uint256</text>
<text text-anchor="start" x="1111.11" y="-2406.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;mint(shares: uint256, receiver: address): uint256</text>
<text text-anchor="start" x="1111.11" y="-2389.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;withdraw(assets: uint256, receiver: address, owner: address): uint256</text>
<text text-anchor="start" x="1111.11" y="-2372.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;redeem(shares: uint256, receiver: address, owner: address): uint256</text>
</g>
<!-- 23&#45;&gt;3 -->
<g id="edge23" class="edge">
<title>23&#45;&gt;3</title>
<path fill="none" stroke="black" d="M1102.86,-2702.01C784.31,-2773.01 276.11,-2940.49 45.33,-3311.2 4.72,-3376.44 -36.12,-4883.87 56.33,-4985.2 152.2,-5090.28 1071.27,-5133.58 1505.36,-5148.28"/>
<polygon fill="black" stroke="black" points="1505.41,-5151.79 1515.52,-5148.63 1505.65,-5144.79 1505.41,-5151.79"/>
</g>
<!-- 23&#45;&gt;7 -->
<g id="edge22" class="edge">
<title>23&#45;&gt;7</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1221.07,-2942.83C1149.91,-3071.67 1067.42,-3221 1001.55,-3340.26"/>
<polygon fill="black" stroke="black" points="998.41,-3338.71 996.64,-3349.15 1004.53,-3342.09 998.41,-3338.71"/>
</g>
<!-- 23&#45;&gt;8 -->
<g id="edge25" class="edge">
<title>23&#45;&gt;8</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1102.74,-2809.1C929.06,-2922.86 720.13,-3095.08 621.33,-3311.2 575.56,-3411.32 580.66,-3704.3 621.33,-3806.6 629.46,-3827.05 643.96,-3823.76 655.33,-3842.6 753.37,-4004.99 810.34,-4223.36 835.25,-4337.01"/>
<polygon fill="black" stroke="black" points="831.88,-4337.98 837.42,-4347.01 838.72,-4336.49 831.88,-4337.98"/>
</g>
<!-- 23&#45;&gt;17 -->
<g id="edge24" class="edge">
<title>23&#45;&gt;17</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1504.55,-2942.86C1548.03,-3055.73 1592.62,-3187.57 1619.33,-3311.2 1665.97,-3527.04 1635.68,-3586.84 1657.33,-3806.6 1676.17,-3997.87 1704.94,-4222.27 1720.12,-4336.98"/>
<polygon fill="black" stroke="black" points="1716.67,-4337.61 1721.46,-4347.06 1723.61,-4336.69 1716.67,-4337.61"/>
</g>
<!-- 23&#45;&gt;19 -->
<g id="edge21" class="edge">
<title>23&#45;&gt;19</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1570.87,-2942.83C1654.17,-3068.88 1750.44,-3214.55 1828.37,-3332.48"/>
<polygon fill="none" stroke="black" points="1819.66,-3338.35 1844.96,-3357.59 1837.18,-3326.77 1819.66,-3338.35"/>
</g>
<!-- 23&#45;&gt;20 -->
<g id="edge26" class="edge">
<title>23&#45;&gt;20</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1102.67,-2880.09C949.67,-3005.54 757.1,-3165.36 588.33,-3311.2 539.24,-3353.62 486.07,-3401.12 439.75,-3443.03"/>
<polygon fill="black" stroke="black" points="437.14,-3440.67 432.08,-3449.98 441.84,-3445.86 437.14,-3440.67"/>
</g>
<!-- 23&#45;&gt;22 -->
<g id="edge20" class="edge">
<title>23&#45;&gt;22</title>
<path fill="none" stroke="black" d="M1380.33,-2942.83C1380.33,-3051.15 1380.33,-3173.97 1380.33,-3281.21"/>
<polygon fill="none" stroke="black" points="1369.83,-3281.39 1380.33,-3311.39 1390.83,-3281.39 1369.83,-3281.39"/>
</g>
<!-- 24 -->
<g id="node25" class="node">
<title>24</title>
<polygon fill="#f2f2f2" stroke="black" points="2902.59,-2032.3 2902.59,-3274.7 3730.08,-3274.7 3730.08,-2032.3 2902.59,-2032.3"/>
<text text-anchor="middle" x="3316.33" y="-3258.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="3316.33" y="-3241.3" font-family="Times,serif" font-size="14.00">IPToken</text>
<text text-anchor="middle" x="3316.33" y="-3224.5" font-family="Times,serif" font-size="14.00">public/flatten/RiskEngineModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="2902.59,-3216.3 3730.08,-3216.3 "/>
<text text-anchor="start" x="2910.59" y="-3199.7" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="2910.59" y="-3182.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;mint(tokenAmount: uint256, receiver: address): uint256</text>
<text text-anchor="start" x="2910.59" y="-3166.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;deposit(mintAmount: uint256, receiver: address): uint256</text>
<text text-anchor="start" x="2910.59" y="-3149.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;redeem(redeemTokens: uint256, receiver: address, owner: address): uint256</text>
<text text-anchor="start" x="2910.59" y="-3132.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;withdraw(redeemAmount: uint256, receiver: address, owner: address): uint256</text>
<text text-anchor="start" x="2910.59" y="-3115.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;borrow(borrowAmount: uint256)</text>
<text text-anchor="start" x="2910.59" y="-3098.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;borrowOnBehalfOf(onBehalfOf: address, borrowAmount: uint256)</text>
<text text-anchor="start" x="2910.59" y="-3082.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;repayBorrow(repayAmount: uint256)</text>
<text text-anchor="start" x="2910.59" y="-3065.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;repayBorrowOnBehalfOf(onBehalfOf: address, repayAmount: uint256)</text>
<text text-anchor="start" x="2910.59" y="-3048.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;liquidateBorrow(borrower: address, repayAmount: uint256, pTokenCollateral: IPToken)</text>
<text text-anchor="start" x="2910.59" y="-3031.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;accrueInterest()</text>
<text text-anchor="start" x="2910.59" y="-3014.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;seize(liquidator: address, borrower: address, seizeTokens: uint256)</text>
<text text-anchor="start" x="2910.59" y="-2998.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;addReserves(addAmount: uint256)</text>
<text text-anchor="start" x="2910.59" y="-2981.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setReserveFactor(newReserveFactorMantissa: uint256)</text>
<text text-anchor="start" x="2910.59" y="-2964.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setBorrowRateMax(newBorrowRateMaxMantissa: uint256)</text>
<text text-anchor="start" x="2910.59" y="-2947.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setProtocolSeizeShare(newProtocolSeizeShareMantissa: uint256)</text>
<text text-anchor="start" x="2910.59" y="-2930.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;reduceReservesEmergency(reduceAmount: uint256)</text>
<text text-anchor="start" x="2910.59" y="-2914.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;reduceReservesOwner(reduceAmount: uint256)</text>
<text text-anchor="start" x="2910.59" y="-2897.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;reduceReservesConfigurator(reduceAmount: uint256)</text>
<text text-anchor="start" x="2910.59" y="-2880.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;sweepToken(token: IERC20)</text>
<text text-anchor="start" x="2910.59" y="-2863.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;borrowRateMaxMantissa(): uint256</text>
<text text-anchor="start" x="2910.59" y="-2846.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;accrualBlockTimestamp(): uint256</text>
<text text-anchor="start" x="2910.59" y="-2830.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;exchangeRateCurrent(): uint256</text>
<text text-anchor="start" x="2910.59" y="-2813.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;balanceOfUnderlying(owner: address): uint256</text>
<text text-anchor="start" x="2910.59" y="-2796.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getAccountSnapshot(account: address): (uint256, uint256, uint256)</text>
<text text-anchor="start" x="2910.59" y="-2779.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;totalBorrowsCurrent(): uint256</text>
<text text-anchor="start" x="2910.59" y="-2762.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;totalReservesCurrent(): uint256</text>
<text text-anchor="start" x="2910.59" y="-2746.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;ownerReservesCurrent(): uint256</text>
<text text-anchor="start" x="2910.59" y="-2729.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;configuratorReservesCurrent(): uint256</text>
<text text-anchor="start" x="2910.59" y="-2712.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;borrowBalanceCurrent(account: address): uint256</text>
<text text-anchor="start" x="2910.59" y="-2695.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;borrowBalanceStored(account: address): uint256</text>
<text text-anchor="start" x="2910.59" y="-2678.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;exchangeRateStored(): uint256</text>
<text text-anchor="start" x="2910.59" y="-2662.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getCash(): uint256</text>
<text text-anchor="start" x="2910.59" y="-2645.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;borrowRatePerSecond(): uint256</text>
<text text-anchor="start" x="2910.59" y="-2628.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;supplyRatePerSecond(): uint256</text>
<text text-anchor="start" x="2910.59" y="-2611.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;totalBorrows(): uint256</text>
<text text-anchor="start" x="2910.59" y="-2594.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;totalReserves(): uint256</text>
<text text-anchor="start" x="2910.59" y="-2578.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;ownerReserves(): uint256</text>
<text text-anchor="start" x="2910.59" y="-2561.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;configuratorReserves(): uint256</text>
<text text-anchor="start" x="2910.59" y="-2544.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;borrowIndex(): uint256</text>
<text text-anchor="start" x="2910.59" y="-2527.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;reserveFactorMantissa(): uint256</text>
<text text-anchor="start" x="2910.59" y="-2510.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;riskEngine(): IRiskEngine</text>
<text text-anchor="start" x="2910.59" y="-2494.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;totalSupply(): uint256</text>
<text text-anchor="start" x="2910.59" y="-2477.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;name(): string</text>
<text text-anchor="start" x="2910.59" y="-2460.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;symbol(): string</text>
<text text-anchor="start" x="2910.59" y="-2443.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;decimals(): uint8</text>
<text text-anchor="start" x="2910.59" y="-2426.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;asset(): address</text>
<text text-anchor="start" x="2910.59" y="-2410.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;protocolSeizeShareMantissa(): uint256</text>
<text text-anchor="start" x="2910.59" y="-2393.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;convertToShares(assets: uint256): uint256</text>
<text text-anchor="start" x="2910.59" y="-2376.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;convertToAssets(shares: uint256): uint256</text>
<text text-anchor="start" x="2910.59" y="-2359.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;maxMint(receiver: address): uint256</text>
<text text-anchor="start" x="2910.59" y="-2342.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;maxDeposit(account: address): uint256</text>
<text text-anchor="start" x="2910.59" y="-2326.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;maxRedeem(owner: address): (maxShares: uint256)</text>
<text text-anchor="start" x="2910.59" y="-2309.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;maxWithdraw(owner: address): uint256</text>
<text text-anchor="start" x="2910.59" y="-2292.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;previewDeposit(assets: uint256): (shares: uint256)</text>
<text text-anchor="start" x="2910.59" y="-2275.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;previewMint(shares: uint256): (assets: uint256)</text>
<text text-anchor="start" x="2910.59" y="-2258.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;previewWithdraw(assets: uint256): (shares: uint256)</text>
<text text-anchor="start" x="2910.59" y="-2242.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;previewRedeem(shares: uint256): (assets: uint256)</text>
<text text-anchor="start" x="2910.59" y="-2225.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;totalAssets(): uint256</text>
<text text-anchor="start" x="2910.59" y="-2208.5" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="2910.59" y="-2191.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewRiskEngine(oldRiskEngine: IRiskEngine, newRiskEngine: IRiskEngine)</text>
<text text-anchor="start" x="2910.59" y="-2174.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; Borrow(borrower: address, onBehalfOf: address, borrowAmount: uint256, accountBorrows: uint256, totalBorrows: uint256)</text>
<text text-anchor="start" x="2910.59" y="-2158.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; RepayBorrow(payer: address, onBehalfOf: address, repayAmount: uint256, accountBorrows: uint256, totalBorrows: uint256)</text>
<text text-anchor="start" x="2910.59" y="-2141.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewReserveFactor(oldReserveFactorMantissa: uint256, newReserveFactorMantissa: uint256)</text>
<text text-anchor="start" x="2910.59" y="-2124.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewBorrowRateMax(oldBorrowRateMaxMantissa: uint256, newBorrowRateMaxMantissa: uint256)</text>
<text text-anchor="start" x="2910.59" y="-2107.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewProtocolSeizeShare(oldProtocolSeizeShareMantissa: uint256, newProtocolSeizeShareMantissa: uint256)</text>
<text text-anchor="start" x="2910.59" y="-2090.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; AccrueInterest(cashPrior: uint256, totalReserves: uint256, borrowIndex: uint256, totalBorrows: uint256)</text>
<text text-anchor="start" x="2910.59" y="-2074.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; LiquidateBorrow(liquidator: address, borrower: address, repayAmount: uint256, pTokenCollateral: address, seizeTokens: uint256)</text>
<text text-anchor="start" x="2910.59" y="-2057.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; ReservesAdded(benefactor: address, addAmount: uint256, newTotalReserves: uint256)</text>
<text text-anchor="start" x="2910.59" y="-2040.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; ReservesReduced(admin: address, reduceAmount: uint256, newTotalReserves: uint256)</text>
</g>
<!-- 24&#45;&gt;3 -->
<g id="edge30" class="edge">
<title>24&#45;&gt;3</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M3039.6,-3274.72C2798.9,-3771.46 2416.87,-4468.25 1956.33,-4985.2 1941.97,-5001.32 1925.87,-5016.9 1909.05,-5031.66"/>
<polygon fill="black" stroke="black" points="1906.56,-5029.19 1901.28,-5038.38 1911.13,-5034.49 1906.56,-5029.19"/>
</g>
<!-- 24&#45;&gt;19 -->
<g id="edge27" class="edge">
<title>24&#45;&gt;19</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2902.19,-3270C2899.25,-3271.76 2896.3,-3273.5 2893.33,-3275.2 2793.84,-3332.32 2487.56,-3276.13 2378.33,-3311.2 2350.39,-3320.17 2322.39,-3331.48 2294.91,-3344.29"/>
<polygon fill="none" stroke="black" points="2290,-3335 2267.63,-3357.58 2299.2,-3353.89 2290,-3335"/>
</g>
<!-- 24&#45;&gt;24 -->
<g id="edge29" class="edge">
<title>24&#45;&gt;24</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M3730.24,-2567.33C3741.65,-2591.01 3748.08,-2619.73 3748.08,-2653.5 3748.08,-2682.91 3743.2,-2708.5 3734.4,-2730.26"/>
<polygon fill="black" stroke="black" points="3731.09,-2729.11 3730.24,-2739.67 3737.49,-2731.94 3731.09,-2729.11"/>
</g>
<!-- 25 -->
<g id="node26" class="node">
<title>25</title>
<polygon fill="#f2f2f2" stroke="black" points="4100.8,-3843.1 4100.8,-4984.7 5093.87,-4984.7 5093.87,-3843.1 4100.8,-3843.1"/>
<text text-anchor="middle" x="4597.33" y="-4968.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="4597.33" y="-4951.3" font-family="Times,serif" font-size="14.00">IRiskEngine</text>
<text text-anchor="middle" x="4597.33" y="-4934.5" font-family="Times,serif" font-size="14.00">public/flatten/RiskEngineModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="4100.8,-4926.3 5093.87,-4926.3 "/>
<text text-anchor="start" x="4108.8" y="-4909.7" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="4108.8" y="-4892.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setReserveShares(newOwnerShareMantissa: uint256, newConfiguratorShareMantissa: uint256)</text>
<text text-anchor="start" x="4108.8" y="-4876.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;switchEMode(newCategoryId: uint8)</text>
<text text-anchor="start" x="4108.8" y="-4859.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;enterMarkets(pTokens: address[]): uint256[]</text>
<text text-anchor="start" x="4108.8" y="-4842.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;exitMarket(pTokenAddress: address)</text>
<text text-anchor="start" x="4108.8" y="-4825.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;updateDelegate(delegate: address, approved: bool)</text>
<text text-anchor="start" x="4108.8" y="-4808.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;mintVerify(account: address)</text>
<text text-anchor="start" x="4108.8" y="-4792.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;repayBorrowVerify(pToken: IPToken, account: address)</text>
<text text-anchor="start" x="4108.8" y="-4775.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;borrowAllowed(pToken: address, borrower: address, borrowAmount: uint256): RiskEngineError.Error</text>
<text text-anchor="start" x="4108.8" y="-4758.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setOracle(newOracle: address)</text>
<text text-anchor="start" x="4108.8" y="-4741.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;configureEMode(categoryId: uint8, baseConfig: BaseConfiguration)</text>
<text text-anchor="start" x="4108.8" y="-4724.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setCloseFactor(pTokenAddress: address, newCloseFactorMantissa: uint256)</text>
<text text-anchor="start" x="4108.8" y="-4708.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;configureMarket(pToken: IPToken, baseConfig: BaseConfiguration)</text>
<text text-anchor="start" x="4108.8" y="-4691.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;supportMarket(pToken: IPToken)</text>
<text text-anchor="start" x="4108.8" y="-4674.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;supportEMode(categoryId: uint8, isAllowed: bool, pTokens: address[], collateralPermissions: bool[], borrowPermissions: bool[])</text>
<text text-anchor="start" x="4108.8" y="-4657.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setMarketBorrowCaps(pTokens: IPToken[], newBorrowCaps: uint256[])</text>
<text text-anchor="start" x="4108.8" y="-4640.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setMarketSupplyCaps(pTokens: IPToken[], newSupplyCaps: uint256[])</text>
<text text-anchor="start" x="4108.8" y="-4624.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setMintPaused(pToken: IPToken, state: bool): bool</text>
<text text-anchor="start" x="4108.8" y="-4607.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setBorrowPaused(pToken: IPToken, state: bool): bool</text>
<text text-anchor="start" x="4108.8" y="-4590.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setTransferPaused(state: bool): bool</text>
<text text-anchor="start" x="4108.8" y="-4573.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setSeizePaused(state: bool): bool</text>
<text text-anchor="start" x="4108.8" y="-4556.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getAssetsIn(account: address): IPToken[]</text>
<text text-anchor="start" x="4108.8" y="-4540.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getReserveShares(): (ownerShareMantissa: uint256, configuratorShareMantissa: uint256)</text>
<text text-anchor="start" x="4108.8" y="-4523.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;checkCollateralMembership(account: address, pToken: IPToken): bool</text>
<text text-anchor="start" x="4108.8" y="-4506.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;checkBorrowMembership(account: address, pToken: IPToken): bool</text>
<text text-anchor="start" x="4108.8" y="-4489.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;accountCategory(account: address): uint8</text>
<text text-anchor="start" x="4108.8" y="-4472.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getAccountLiquidity(account: address): (RiskEngineError.Error, uint256, uint256)</text>
<text text-anchor="start" x="4108.8" y="-4456.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getAccountBorrowLiquidity(account: address): (RiskEngineError.Error, uint256, uint256)</text>
<text text-anchor="start" x="4108.8" y="-4439.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getHypotheticalAccountLiquidity(account: address, pTokenModify: address, redeemTokens: uint256, borrowAmount: uint256): (RiskEngineError.Error, uint256, uint256)</text>
<text text-anchor="start" x="4108.8" y="-4422.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;liquidateCalculateSeizeTokens(borrower: address, pTokenBorrowed: address, pTokenCollateral: address, actualRepayAmount: uint256): (RiskEngineError.Error, uint256)</text>
<text text-anchor="start" x="4108.8" y="-4405.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;delegateAllowed(user: address, delegate: address): bool</text>
<text text-anchor="start" x="4108.8" y="-4388.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getAllMarkets(): IPToken[]</text>
<text text-anchor="start" x="4108.8" y="-4372.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;isDeprecated(pToken: IPToken): bool</text>
<text text-anchor="start" x="4108.8" y="-4355.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;maxWithdraw(pToken: address, account: address): uint256</text>
<text text-anchor="start" x="4108.8" y="-4338.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;mintAllowed(account: address, pToken: address, mintAmount: uint256): RiskEngineError.Error</text>
<text text-anchor="start" x="4108.8" y="-4321.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;redeemAllowed(pToken: address, redeemer: address, redeemTokens: uint256): RiskEngineError.Error</text>
<text text-anchor="start" x="4108.8" y="-4304.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;repayBorrowAllowed(pToken: address): RiskEngineError.Error</text>
<text text-anchor="start" x="4108.8" y="-4288.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;liquidateBorrowAllowed(pTokenBorrowed: address, pTokenCollateral: address, borrower: address, repayAmount: uint256): RiskEngineError.Error</text>
<text text-anchor="start" x="4108.8" y="-4271.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;seizeAllowed(pTokenCollateral: address, pTokenBorrowed: address): RiskEngineError.Error</text>
<text text-anchor="start" x="4108.8" y="-4254.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;transferAllowed(pToken: address, src: address, transferTokens: uint256): RiskEngineError.Error</text>
<text text-anchor="start" x="4108.8" y="-4237.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;oracle(): address</text>
<text text-anchor="start" x="4108.8" y="-4220.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;collateralFactor(categoryId: uint8, pToken: IPToken): uint256</text>
<text text-anchor="start" x="4108.8" y="-4204.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;liquidationThreshold(categoryId: uint8, pToken: IPToken): uint256</text>
<text text-anchor="start" x="4108.8" y="-4187.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;liquidationIncentive(categoryId: uint8, pToken: address): uint256</text>
<text text-anchor="start" x="4108.8" y="-4170.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;closeFactor(pToken: address): uint256</text>
<text text-anchor="start" x="4108.8" y="-4153.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;supplyCap(pToken: address): uint256</text>
<text text-anchor="start" x="4108.8" y="-4136.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;borrowCap(pToken: address): uint256</text>
<text text-anchor="start" x="4108.8" y="-4120.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;emodeMarkets(categoryId: uint8): (collateralTokens: address[], borrowTokens: address[])</text>
<text text-anchor="start" x="4108.8" y="-4103.3" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="4108.8" y="-4086.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewEModeConfiguration(categoryId: uint8, oldConfig: BaseConfiguration, newConfig: BaseConfiguration)</text>
<text text-anchor="start" x="4108.8" y="-4069.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewMarketConfiguration(pToken: IPToken, oldConfig: BaseConfiguration, newConfig: BaseConfiguration)</text>
<text text-anchor="start" x="4108.8" y="-4052.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewOracleEngine(oldOracleEngine: address, newOracleEngine: address)</text>
<text text-anchor="start" x="4108.8" y="-4036.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewReserveShares(newOwnerShareMantissa: uint256, newConfiguratorShareMantissa: uint256)</text>
<text text-anchor="start" x="4108.8" y="-4019.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; MarketListed(pToken: IPToken)</text>
<text text-anchor="start" x="4108.8" y="-4002.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; EModeSwitched(account: address, oldCategory: uint8, newCategory: uint8)</text>
<text text-anchor="start" x="4108.8" y="-3985.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; EModeUpdated(categoryId: uint8, pToken: address, allowed: bool, collateralStatus: bool, borrowStatus: bool)</text>
<text text-anchor="start" x="4108.8" y="-3968.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; MarketEntered(pToken: IPToken, account: address)</text>
<text text-anchor="start" x="4108.8" y="-3952.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; MarketExited(pToken: IPToken, account: address)</text>
<text text-anchor="start" x="4108.8" y="-3935.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewCloseFactor(pToken: address, oldCloseFactorMantissa: uint256, newCloseFactorMantissa: uint256)</text>
<text text-anchor="start" x="4108.8" y="-3918.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; ActionPaused(action: string, pauseState: bool)</text>
<text text-anchor="start" x="4108.8" y="-3901.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; ActionPaused(pToken: IPToken, action: string, pauseState: bool)</text>
<text text-anchor="start" x="4108.8" y="-3884.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewBorrowCap(pToken: IPToken, newBorrowCap: uint256)</text>
<text text-anchor="start" x="4108.8" y="-3868.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewSupplyCap(pToken: IPToken, newSupplyCap: uint256)</text>
<text text-anchor="start" x="4108.8" y="-3851.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; DelegateUpdated(approver: address, delegate: address, approved: bool)</text>
</g>
<!-- 24&#45;&gt;25 -->
<g id="edge28" class="edge">
<title>24&#45;&gt;25</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M3686.36,-3275.06C3799.37,-3450.52 3927.42,-3639.19 4054.33,-3806.6 4067.38,-3823.81 4080.78,-3841.13 4094.46,-3858.51"/>
<polygon fill="black" stroke="black" points="4091.82,-3860.81 4100.76,-3866.49 4097.31,-3856.48 4091.82,-3860.81"/>
</g>
<!-- 25&#45;&gt;10 -->
<g id="edge34" class="edge">
<title>25&#45;&gt;10</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4100.54,-4867.01C3927.52,-5012.22 3727.48,-5167.18 3531.33,-5290.2 3512.45,-5302.04 3491.04,-5313.03 3470.75,-5322.46"/>
<polygon fill="black" stroke="black" points="3469.16,-5319.34 3461.52,-5326.68 3472.07,-5325.7 3469.16,-5319.34"/>
</g>
<!-- 25&#45;&gt;11 -->
<g id="edge35" class="edge">
<title>25&#45;&gt;11</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4100.71,-4719.54C3894.51,-4846.03 3671.17,-4983.04 3531.29,-5068.84"/>
<polygon fill="black" stroke="black" points="3529.31,-5065.95 3522.62,-5074.17 3532.97,-5071.92 3529.31,-5065.95"/>
</g>
<!-- 25&#45;&gt;24 -->
<g id="edge33" class="edge">
<title>25&#45;&gt;24</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4100.49,-3843.22C4090.94,-3830.97 4081.55,-3818.76 4072.33,-3806.6 3947.47,-3641.89 3821.5,-3456.59 3709.14,-3283.55"/>
<polygon fill="black" stroke="black" points="3712.01,-3281.54 3703.63,-3275.06 3706.14,-3285.35 3712.01,-3281.54"/>
</g>
<!-- 26 -->
<g id="node27" class="node">
<title>26</title>
<polygon fill="#f2f2f2" stroke="black" points="4728.08,-5097.3 4728.08,-5214.1 4990.59,-5214.1 4990.59,-5097.3 4728.08,-5097.3"/>
<text text-anchor="middle" x="4859.33" y="-5197.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="4859.33" y="-5180.7" font-family="Times,serif" font-size="14.00">BaseConfiguration</text>
<text text-anchor="middle" x="4859.33" y="-5163.9" font-family="Times,serif" font-size="14.00">public/flatten/RiskEngineModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="4728.08,-5155.7 4990.59,-5155.7 "/>
<text text-anchor="start" x="4736.08" y="-5139.1" font-family="Times,serif" font-size="14.00">collateralFactorMantissa: uint256</text>
<text text-anchor="start" x="4736.08" y="-5122.3" font-family="Times,serif" font-size="14.00">liquidationThresholdMantissa: uint256</text>
<text text-anchor="start" x="4736.08" y="-5105.5" font-family="Times,serif" font-size="14.00">liquidationIncentiveMantissa: uint256</text>
</g>
<!-- 25&#45;&gt;26 -->
<g id="edge32" class="edge">
<title>25&#45;&gt;26</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4793.44,-4984.97C4807.87,-5024.51 4820.89,-5059.68 4831.49,-5087.6"/>
<polygon fill="black" stroke="black" points="4828.3,-5089.07 4835.13,-5097.17 4834.84,-5086.58 4828.3,-5089.07"/>
</g>
<!-- 26&#45;&gt;25 -->
<g id="edge31" class="edge">
<title>26&#45;&gt;25</title>
<path fill="none" stroke="black" d="M4842.79,-5097.17C4834.09,-5070.48 4822.45,-5035.91 4808.96,-4996.5"/>
<polygon fill="black" stroke="black" points="4808.9,-4996.33 4803.17,-4991.95 4805.01,-4984.97 4810.74,-4989.35 4808.9,-4996.33"/>
</g>
<!-- 27 -->
<g id="node28" class="node">
<title>27</title>
<polygon fill="#f2f2f2" stroke="black" points="2956.8,-1605.7 2956.8,-1789.7 3799.87,-1789.7 3799.87,-1605.7 2956.8,-1605.7"/>
<text text-anchor="middle" x="3378.33" y="-1773.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="3378.33" y="-1756.3" font-family="Times,serif" font-size="14.00">IOracleEngine</text>
<text text-anchor="middle" x="3378.33" y="-1739.5" font-family="Times,serif" font-size="14.00">public/flatten/RiskEngineModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="2956.8,-1731.3 3799.87,-1731.3 "/>
<text text-anchor="start" x="2964.8" y="-1714.7" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="2964.8" y="-1697.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setAssetConfig(asset: address, mainOracle: address, fallbackOracle: address, lowerBoundRatio: uint256, upperBoundRatio: uint256)</text>
<text text-anchor="start" x="2964.8" y="-1681.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getUnderlyingPrice(pToken: IPToken): uint256</text>
<text text-anchor="start" x="2964.8" y="-1664.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getPrice(asset: address): uint256</text>
<text text-anchor="start" x="2964.8" y="-1647.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;configs(asset: address): AssetConfig</text>
<text text-anchor="start" x="2964.8" y="-1630.7" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="2964.8" y="-1613.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; AssetConfigSet(asset: address, mainOracle: address, fallbackOracle: address, lowerBoundRatio: uint256, upperBoundRatio: uint256)</text>
</g>
<!-- 27&#45;&gt;24 -->
<g id="edge37" class="edge">
<title>27&#45;&gt;24</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M3372.42,-1789.72C3368.59,-1848.63 3363.23,-1931.1 3357.32,-2021.89"/>
<polygon fill="black" stroke="black" points="3353.81,-2021.93 3356.66,-2032.14 3360.8,-2022.39 3353.81,-2021.93"/>
</g>
<!-- 28 -->
<g id="node29" class="node">
<title>28</title>
<polygon fill="#f2f2f2" stroke="black" points="3766.08,-2586.7 3766.08,-2720.3 4028.59,-2720.3 4028.59,-2586.7 3766.08,-2586.7"/>
<text text-anchor="middle" x="3897.33" y="-2703.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="3897.33" y="-2686.9" font-family="Times,serif" font-size="14.00">AssetConfig</text>
<text text-anchor="middle" x="3897.33" y="-2670.1" font-family="Times,serif" font-size="14.00">public/flatten/RiskEngineModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="3766.08,-2661.9 4028.59,-2661.9 "/>
<text text-anchor="start" x="3774.08" y="-2645.3" font-family="Times,serif" font-size="14.00">mainOracle: address</text>
<text text-anchor="start" x="3774.08" y="-2628.5" font-family="Times,serif" font-size="14.00">fallbackOracle: address</text>
<text text-anchor="start" x="3774.08" y="-2611.7" font-family="Times,serif" font-size="14.00">lowerBoundRatio: uint256</text>
<text text-anchor="start" x="3774.08" y="-2594.9" font-family="Times,serif" font-size="14.00">upperBoundRatio: uint256</text>
</g>
<!-- 27&#45;&gt;28 -->
<g id="edge38" class="edge">
<title>27&#45;&gt;28</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M3512.86,-1789.84C3593,-1849.19 3690.08,-1933.69 3748.33,-2031.8 3852.69,-2207.57 3879.33,-2453.55 3889.48,-2576.4"/>
<polygon fill="black" stroke="black" points="3886,-2576.78 3890.3,-2586.46 3892.98,-2576.21 3886,-2576.78"/>
</g>
<!-- 28&#45;&gt;27 -->
<g id="edge36" class="edge">
<title>28&#45;&gt;27</title>
<path fill="none" stroke="black" d="M3900.91,-2586.46C3898.77,-2467.43 3873.63,-2212.51 3766.33,-2031.8 3710.36,-1937.52 3618.52,-1855.81 3538.22,-1796.91"/>
<polygon fill="black" stroke="black" points="3538.19,-1796.89 3530.99,-1796.6 3528.49,-1789.84 3535.69,-1790.13 3538.19,-1796.89"/>
</g>
<!-- 29 -->
<g id="node30" class="node">
<title>29</title>
<polygon fill="#f2f2f2" stroke="black" points="4692.38,-1601.7 4692.38,-1793.7 5048.28,-1793.7 5048.28,-1601.7 4692.38,-1601.7"/>
<text text-anchor="middle" x="4870.33" y="-1777.1" font-family="Times,serif" font-size="14.00">RiskEngineStorage</text>
<text text-anchor="middle" x="4870.33" y="-1760.3" font-family="Times,serif" font-size="14.00">public/flatten/RiskEngineModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="4692.38,-1752.1 5048.28,-1752.1 "/>
<text text-anchor="start" x="4700.38" y="-1735.5" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="4700.38" y="-1718.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;_SLOT_RISK_ENGINE_STORAGE: bytes32</text>
<text text-anchor="start" x="4700.38" y="-1701.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;_CLOSE_FACTOR_MIN_MANTISSA: uint256</text>
<text text-anchor="start" x="4700.38" y="-1685.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;_CLOSE_FACTOR_MAX_MANTISSA: uint256</text>
<text text-anchor="start" x="4700.38" y="-1668.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;_COLLATERAL_FACTOR_MAX_MANTISSA: uint256</text>
<text text-anchor="start" x="4700.38" y="-1651.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;_MANTISSA_ONE: uint256</text>
<polyline fill="none" stroke="black" points="4692.38,-1643.3 5048.28,-1643.3 "/>
<text text-anchor="start" x="4700.38" y="-1626.7" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="4700.38" y="-1609.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_getRiskEngineStorage(): (data: RiskEngineData)</text>
</g>
<!-- 30 -->
<g id="node31" class="node">
<title>30</title>
<polygon fill="#f2f2f2" stroke="black" points="4730.46,-2469.1 4730.46,-2837.9 5118.21,-2837.9 5118.21,-2469.1 4730.46,-2469.1"/>
<text text-anchor="middle" x="4924.33" y="-2821.3" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="4924.33" y="-2804.5" font-family="Times,serif" font-size="14.00">RiskEngineData</text>
<text text-anchor="middle" x="4924.33" y="-2787.7" font-family="Times,serif" font-size="14.00">public/flatten/RiskEngineModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="4730.46,-2779.5 5118.21,-2779.5 "/>
<text text-anchor="start" x="4738.46" y="-2762.9" font-family="Times,serif" font-size="14.00">closeFactorMantissa: mapping(address=&gt;uint256)</text>
<text text-anchor="start" x="4738.46" y="-2746.1" font-family="Times,serif" font-size="14.00">accountCategory: mapping(address=&gt;uint8)</text>
<text text-anchor="start" x="4738.46" y="-2729.3" font-family="Times,serif" font-size="14.00">accountAssets: mapping(address=&gt;IPToken[])</text>
<text text-anchor="start" x="4738.46" y="-2712.5" font-family="Times,serif" font-size="14.00">collateralCategory: mapping(uint8=&gt;mapping(address=&gt;bool))</text>
<text text-anchor="start" x="4738.46" y="-2695.7" font-family="Times,serif" font-size="14.00">borrowCategory: mapping(uint8=&gt;mapping(address=&gt;bool))</text>
<text text-anchor="start" x="4738.46" y="-2678.9" font-family="Times,serif" font-size="14.00">markets: mapping(address=&gt;Market)</text>
<text text-anchor="start" x="4738.46" y="-2662.1" font-family="Times,serif" font-size="14.00">emodes: mapping(uint8=&gt;EModeConfiguration)</text>
<text text-anchor="start" x="4738.46" y="-2645.3" font-family="Times,serif" font-size="14.00">oracle: address</text>
<text text-anchor="start" x="4738.46" y="-2628.5" font-family="Times,serif" font-size="14.00">transferGuardianPaused: bool</text>
<text text-anchor="start" x="4738.46" y="-2611.7" font-family="Times,serif" font-size="14.00">seizeGuardianPaused: bool</text>
<text text-anchor="start" x="4738.46" y="-2594.9" font-family="Times,serif" font-size="14.00">mintGuardianPaused: mapping(address=&gt;bool)</text>
<text text-anchor="start" x="4738.46" y="-2578.1" font-family="Times,serif" font-size="14.00">borrowGuardianPaused: mapping(address=&gt;bool)</text>
<text text-anchor="start" x="4738.46" y="-2561.3" font-family="Times,serif" font-size="14.00">borrowCaps: mapping(address=&gt;uint256)</text>
<text text-anchor="start" x="4738.46" y="-2544.5" font-family="Times,serif" font-size="14.00">supplyCaps: mapping(address=&gt;uint256)</text>
<text text-anchor="start" x="4738.46" y="-2527.7" font-family="Times,serif" font-size="14.00">approvedDelegates: mapping(address=&gt;mapping(address=&gt;bool))</text>
<text text-anchor="start" x="4738.46" y="-2510.9" font-family="Times,serif" font-size="14.00">allMarkets: mapping(uint8=&gt;IPToken[])</text>
<text text-anchor="start" x="4738.46" y="-2494.1" font-family="Times,serif" font-size="14.00">ownerShareMantissa: uint256</text>
<text text-anchor="start" x="4738.46" y="-2477.3" font-family="Times,serif" font-size="14.00">configuratorShareMantissa: uint256</text>
</g>
<!-- 29&#45;&gt;30 -->
<g id="edge43" class="edge">
<title>29&#45;&gt;30</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4871.48,-1794.08C4877.41,-1948.58 4894.42,-2256.61 4907.8,-2458.57"/>
<polygon fill="black" stroke="black" points="4904.31,-2458.96 4908.47,-2468.71 4911.3,-2458.5 4904.31,-2458.96"/>
</g>
<!-- 30&#45;&gt;29 -->
<g id="edge39" class="edge">
<title>30&#45;&gt;29</title>
<path fill="none" stroke="black" d="M4919.38,-2468.71C4910.27,-2272.17 4892.67,-1967.16 4880.89,-1806.35"/>
<polygon fill="black" stroke="black" points="4880.87,-1806.05 4876.44,-1800.36 4879.98,-1794.08 4884.42,-1799.77 4880.87,-1806.05"/>
</g>
<!-- 32 -->
<g id="node33" class="node">
<title>32</title>
<polygon fill="#f2f2f2" stroke="black" points="4654.3,-3492.1 4654.3,-3625.7 4960.36,-3625.7 4960.36,-3492.1 4654.3,-3492.1"/>
<text text-anchor="middle" x="4807.33" y="-3609.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="4807.33" y="-3592.3" font-family="Times,serif" font-size="14.00">Market</text>
<text text-anchor="middle" x="4807.33" y="-3575.5" font-family="Times,serif" font-size="14.00">public/flatten/RiskEngineModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="4654.3,-3567.3 4960.36,-3567.3 "/>
<text text-anchor="start" x="4662.3" y="-3550.7" font-family="Times,serif" font-size="14.00">isListed: bool</text>
<text text-anchor="start" x="4662.3" y="-3533.9" font-family="Times,serif" font-size="14.00">baseConfiguration: IRiskEngine.BaseConfiguration</text>
<text text-anchor="start" x="4662.3" y="-3517.1" font-family="Times,serif" font-size="14.00">collateralMembership: mapping(address=&gt;bool)</text>
<text text-anchor="start" x="4662.3" y="-3500.3" font-family="Times,serif" font-size="14.00">borrowMembership: mapping(address=&gt;bool)</text>
</g>
<!-- 30&#45;&gt;32 -->
<g id="edge44" class="edge">
<title>30&#45;&gt;32</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4900.55,-2838.13C4874.95,-3035.78 4835.46,-3340.7 4817.21,-3481.65"/>
<polygon fill="black" stroke="black" points="4813.69,-3481.55 4815.88,-3491.92 4820.63,-3482.45 4813.69,-3481.55"/>
</g>
<!-- 33 -->
<g id="node34" class="node">
<title>33</title>
<polygon fill="#f2f2f2" stroke="black" points="4985.3,-3508.9 4985.3,-3608.9 5291.36,-3608.9 5291.36,-3508.9 4985.3,-3508.9"/>
<text text-anchor="middle" x="5138.33" y="-3592.3" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="5138.33" y="-3575.5" font-family="Times,serif" font-size="14.00">EModeConfiguration</text>
<text text-anchor="middle" x="5138.33" y="-3558.7" font-family="Times,serif" font-size="14.00">public/flatten/RiskEngineModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="4985.3,-3550.5 5291.36,-3550.5 "/>
<text text-anchor="start" x="4993.3" y="-3533.9" font-family="Times,serif" font-size="14.00">allowed: bool</text>
<text text-anchor="start" x="4993.3" y="-3517.1" font-family="Times,serif" font-size="14.00">baseConfiguration: IRiskEngine.BaseConfiguration</text>
</g>
<!-- 30&#45;&gt;33 -->
<g id="edge45" class="edge">
<title>30&#45;&gt;33</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4967.83,-2838.13C5016.66,-3044.25 5093.12,-3367.05 5124.35,-3498.86"/>
<polygon fill="black" stroke="black" points="5120.98,-3499.84 5126.69,-3508.76 5127.79,-3498.23 5120.98,-3499.84"/>
</g>
<!-- 31 -->
<g id="node32" class="node">
<title>31</title>
<polygon fill="#f2f2f2" stroke="black" points="4261.08,-1078.3 4261.08,-1363.1 4523.59,-1363.1 4523.59,-1078.3 4261.08,-1078.3"/>
<text text-anchor="middle" x="4392.33" y="-1346.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="4392.33" y="-1329.7" font-family="Times,serif" font-size="14.00">AccountLiquidityLocalVars</text>
<text text-anchor="middle" x="4392.33" y="-1312.9" font-family="Times,serif" font-size="14.00">public/flatten/RiskEngineModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="4261.08,-1304.7 4523.59,-1304.7 "/>
<text text-anchor="start" x="4269.08" y="-1288.1" font-family="Times,serif" font-size="14.00">oracle: IOracleEngine</text>
<text text-anchor="start" x="4269.08" y="-1271.3" font-family="Times,serif" font-size="14.00">accountCategory: uint8</text>
<text text-anchor="start" x="4269.08" y="-1254.5" font-family="Times,serif" font-size="14.00">sumLiquidity: uint256</text>
<text text-anchor="start" x="4269.08" y="-1237.7" font-family="Times,serif" font-size="14.00">sumCollateral: uint256</text>
<text text-anchor="start" x="4269.08" y="-1220.9" font-family="Times,serif" font-size="14.00">sumBorrowPlusEffects: uint256</text>
<text text-anchor="start" x="4269.08" y="-1204.1" font-family="Times,serif" font-size="14.00">pTokenBalance: uint256</text>
<text text-anchor="start" x="4269.08" y="-1187.3" font-family="Times,serif" font-size="14.00">borrowBalance: uint256</text>
<text text-anchor="start" x="4269.08" y="-1170.5" font-family="Times,serif" font-size="14.00">exchangeRateMantissa: uint256</text>
<text text-anchor="start" x="4269.08" y="-1153.7" font-family="Times,serif" font-size="14.00">oraclePriceMantissa: uint256</text>
<text text-anchor="start" x="4269.08" y="-1136.9" font-family="Times,serif" font-size="14.00">threshold: ExponentialNoError.Exp</text>
<text text-anchor="start" x="4269.08" y="-1120.1" font-family="Times,serif" font-size="14.00">exchangeRate: ExponentialNoError.Exp</text>
<text text-anchor="start" x="4269.08" y="-1103.3" font-family="Times,serif" font-size="14.00">oraclePrice: ExponentialNoError.Exp</text>
<text text-anchor="start" x="4269.08" y="-1086.5" font-family="Times,serif" font-size="14.00">tokensToDenom: ExponentialNoError.Exp</text>
</g>
<!-- 31&#45;&gt;16 -->
<g id="edge47" class="edge">
<title>31&#45;&gt;16</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4379.22,-1363.25C4378.42,-1371.89 4377.6,-1380.74 4376.77,-1389.73"/>
<polygon fill="black" stroke="black" points="4373.26,-1389.66 4375.82,-1399.94 4380.23,-1390.3 4373.26,-1389.66"/>
</g>
<!-- 31&#45;&gt;27 -->
<g id="edge46" class="edge">
<title>31&#45;&gt;27</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4260.99,-1339.49C4245.97,-1348.89 4230.29,-1357.21 4214.33,-1363.6 4046.57,-1430.77 3979.33,-1338.3 3809.33,-1399.6 3687.59,-1443.5 3567.92,-1530.5 3485.69,-1599.06"/>
<polygon fill="black" stroke="black" points="3483.27,-1596.52 3477.85,-1605.63 3487.76,-1601.89 3483.27,-1596.52"/>
</g>
<!-- 31&#45;&gt;29 -->
<g id="edge40" class="edge">
<title>31&#45;&gt;29</title>
<path fill="none" stroke="black" d="M4523.62,-1324.86C4551.95,-1348.61 4581.21,-1374.34 4607.33,-1399.6 4670.08,-1460.29 4735.15,-1533.69 4784.87,-1592.35"/>
<polygon fill="black" stroke="black" points="4784.97,-1592.47 4791.89,-1594.47 4792.71,-1601.64 4785.78,-1599.64 4784.97,-1592.47"/>
</g>
<!-- 32&#45;&gt;25 -->
<g id="edge48" class="edge">
<title>32&#45;&gt;25</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4791.12,-3625.77C4778.81,-3675.76 4760.55,-3749.92 4740.09,-3833.04"/>
<polygon fill="black" stroke="black" points="4736.64,-3832.41 4737.65,-3842.95 4743.44,-3834.08 4736.64,-3832.41"/>
</g>
<!-- 32&#45;&gt;26 -->
<g id="edge49" class="edge">
<title>32&#45;&gt;26</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4836.48,-3625.88C4864.53,-3681.95 4912.15,-3760.47 4976.33,-3806.6 5023.97,-3840.84 5067.95,-3795.8 5103.33,-3842.6 5141.61,-3893.24 5132.75,-4928.95 5103.33,-4985.2 5079.91,-5029.99 5038.66,-5065.33 4996.67,-5091.89"/>
<polygon fill="black" stroke="black" points="4994.66,-5089.02 4987.99,-5097.25 4998.34,-5094.97 4994.66,-5089.02"/>
</g>
<!-- 32&#45;&gt;29 -->
<g id="edge41" class="edge">
<title>32&#45;&gt;29</title>
<path fill="none" stroke="black" d="M4781.24,-3492.06C4760.33,-3435.57 4732.67,-3351.44 4721.33,-3275.2 4680.68,-3001.9 4663.65,-2302.02 4721.33,-2031.8 4738.37,-1952 4775.98,-1868.18 4809.15,-1804.59"/>
<polygon fill="black" stroke="black" points="4809.26,-1804.37 4808.52,-1797.2 4814.85,-1793.76 4815.6,-1800.93 4809.26,-1804.37"/>
</g>
<!-- 33&#45;&gt;25 -->
<g id="edge50" class="edge">
<title>33&#45;&gt;25</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M5107.11,-3609.14C5075.68,-3658.69 5023.47,-3741.01 4964.15,-3834.54"/>
<polygon fill="black" stroke="black" points="4961.16,-3832.72 4958.76,-3843.04 4967.07,-3836.47 4961.16,-3832.72"/>
</g>
<!-- 33&#45;&gt;26 -->
<g id="edge51" class="edge">
<title>33&#45;&gt;26</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M5153.76,-3608.97C5210.91,-3800.44 5393.97,-4510.16 5129.33,-4985.2 5100.87,-5036.29 5049.92,-5074.07 4999.88,-5100.84"/>
<polygon fill="black" stroke="black" points="4998.12,-5097.81 4990.88,-5105.54 5001.36,-5104.01 4998.12,-5097.81"/>
</g>
<!-- 33&#45;&gt;29 -->
<g id="edge42" class="edge">
<title>33&#45;&gt;29</title>
<path fill="none" stroke="black" d="M5139.84,-3508.65C5147.12,-3262.73 5176.34,-2173.86 5127.33,-2031.8 5097.48,-1945.26 5035.25,-1863.61 4979.51,-1802.81"/>
<polygon fill="black" stroke="black" points="4979.5,-1802.8 4972.48,-1801.11 4971.34,-1793.99 4978.35,-1795.68 4979.5,-1802.8"/>
</g>
<!-- 34 -->
<g id="node35" class="node">
<title>34</title>
<polygon fill="#f2f2f2" stroke="black" points="3727.26,-0.5 3727.26,-1041.3 5057.41,-1041.3 5057.41,-0.5 3727.26,-0.5"/>
<text text-anchor="middle" x="4392.33" y="-1024.7" font-family="Times,serif" font-size="14.00">RiskEngineModule</text>
<text text-anchor="middle" x="4392.33" y="-1007.9" font-family="Times,serif" font-size="14.00">public/flatten/RiskEngineModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="3727.26,-999.7 5057.41,-999.7 "/>
<text text-anchor="start" x="3735.26" y="-983.1" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="3735.26" y="-966.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_addMarketInternal(pToken: address)</text>
<text text-anchor="start" x="3735.26" y="-949.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;addToMarketCollateralInternal(pToken: IPToken, supplier: address): RiskEngineError.Error</text>
<text text-anchor="start" x="3735.26" y="-932.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;addToMarketBorrowInternal(pToken: IPToken, borrower: address): RiskEngineError.Error</text>
<text text-anchor="start" x="3735.26" y="-915.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getAccountLiquidityInternal(account: address): (RiskEngineError.Error, uint256, uint256)</text>
<text text-anchor="start" x="3735.26" y="-899.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;redeemAllowedInternal(pToken: address, redeemer: address, redeemTokens: uint256): RiskEngineError.Error</text>
<text text-anchor="start" x="3735.26" y="-882.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getHypotheticalAccountLiquidityInternal(account: address, pTokenModify: IPToken, categoryId: uint8, redeemTokens: uint256, borrowAmount: uint256, threshold: FunctionTypeName()): (RiskEngineError.Error, uint256, uint256)</text>
<text text-anchor="start" x="3735.26" y="-865.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getWithdrawLiquidityInternal(account: address, threshold: FunctionTypeName()): (RiskEngineError.Error, uint256)</text>
<text text-anchor="start" x="3735.26" y="-848.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_getCollateralFactor(asset: IPToken, emodeCategory: uint8): ExponentialNoError.Exp</text>
<text text-anchor="start" x="3735.26" y="-831.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_getLiquidationThreshold(asset: IPToken, emodeCategory: uint8): ExponentialNoError.Exp</text>
<text text-anchor="start" x="3735.26" y="-815.1" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="3735.26" y="-798.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;setOracle(newOracle: address)</text>
<text text-anchor="start" x="3735.26" y="-781.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;setReserveShares(newOwnerShareMantissa: uint256, newConfiguratorShareMantissa: uint256)</text>
<text text-anchor="start" x="3735.26" y="-764.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;setCloseFactor(pTokenAddress: address, newCloseFactorMantissa: uint256)</text>
<text text-anchor="start" x="3735.26" y="-747.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;configureMarket(pToken: IPToken, baseConfig: BaseConfiguration)</text>
<text text-anchor="start" x="3735.26" y="-731.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;supportMarket(pToken: IPToken)</text>
<text text-anchor="start" x="3735.26" y="-714.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;supportEMode(categoryId: uint8, isAllowed: bool, pTokens: address[], collateralPermissions: bool[], borrowPermissions: bool[])</text>
<text text-anchor="start" x="3735.26" y="-697.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;configureEMode(categoryId: uint8, baseConfig: BaseConfiguration)</text>
<text text-anchor="start" x="3735.26" y="-680.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;setMarketBorrowCaps(pTokens: IPToken[], newBorrowCaps: uint256[])</text>
<text text-anchor="start" x="3735.26" y="-663.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;setMarketSupplyCaps(pTokens: IPToken[], newSupplyCaps: uint256[])</text>
<text text-anchor="start" x="3735.26" y="-647.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;setMintPaused(pToken: IPToken, state: bool): bool</text>
<text text-anchor="start" x="3735.26" y="-630.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;setBorrowPaused(pToken: IPToken, state: bool): bool</text>
<text text-anchor="start" x="3735.26" y="-613.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;setTransferPaused(state: bool): bool</text>
<text text-anchor="start" x="3735.26" y="-596.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;setSeizePaused(state: bool): bool</text>
<text text-anchor="start" x="3735.26" y="-579.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;switchEMode(newCategoryId: uint8)</text>
<text text-anchor="start" x="3735.26" y="-563.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;enterMarkets(pTokens: address[]): uint256[]</text>
<text text-anchor="start" x="3735.26" y="-546.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;exitMarket(pTokenAddress: address)</text>
<text text-anchor="start" x="3735.26" y="-529.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;updateDelegate(delegate: address, approved: bool)</text>
<text text-anchor="start" x="3735.26" y="-512.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;mintVerify(account: address)</text>
<text text-anchor="start" x="3735.26" y="-495.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;repayBorrowVerify(pToken: IPToken, account: address)</text>
<text text-anchor="start" x="3735.26" y="-479.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;borrowAllowed(pToken: address, borrower: address, borrowAmount: uint256): RiskEngineError.Error</text>
<text text-anchor="start" x="3735.26" y="-462.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;mintAllowed(account: address, pToken: address, mintAmount: uint256): RiskEngineError.Error</text>
<text text-anchor="start" x="3735.26" y="-445.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;redeemAllowed(pToken: address, redeemer: address, redeemTokens: uint256): RiskEngineError.Error</text>
<text text-anchor="start" x="3735.26" y="-428.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;repayBorrowAllowed(pToken: address): RiskEngineError.Error</text>
<text text-anchor="start" x="3735.26" y="-411.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;liquidateBorrowAllowed(pTokenBorrowed: address, pTokenCollateral: address, borrower: address, repayAmount: uint256): RiskEngineError.Error</text>
<text text-anchor="start" x="3735.26" y="-395.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;seizeAllowed(pTokenCollateral: address, pTokenBorrowed: address): RiskEngineError.Error</text>
<text text-anchor="start" x="3735.26" y="-378.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;transferAllowed(pToken: address, src: address, transferTokens: uint256): RiskEngineError.Error</text>
<text text-anchor="start" x="3735.26" y="-361.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;maxWithdraw(pToken: address, account: address): uint256</text>
<text text-anchor="start" x="3735.26" y="-344.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getAssetsIn(account: address): IPToken[]</text>
<text text-anchor="start" x="3735.26" y="-327.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;checkCollateralMembership(account: address, pToken: IPToken): bool</text>
<text text-anchor="start" x="3735.26" y="-311.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;checkBorrowMembership(account: address, pToken: IPToken): bool</text>
<text text-anchor="start" x="3735.26" y="-294.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;accountCategory(account: address): uint8</text>
<text text-anchor="start" x="3735.26" y="-277.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getAccountLiquidity(account: address): (RiskEngineError.Error, uint256, uint256)</text>
<text text-anchor="start" x="3735.26" y="-260.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getAccountBorrowLiquidity(account: address): (RiskEngineError.Error, uint256, uint256)</text>
<text text-anchor="start" x="3735.26" y="-243.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getHypotheticalAccountLiquidity(account: address, pTokenModify: address, redeemTokens: uint256, borrowAmount: uint256): (RiskEngineError.Error, uint256, uint256)</text>
<text text-anchor="start" x="3735.26" y="-227.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;liquidateCalculateSeizeTokens(borrower: address, pTokenBorrowed: address, pTokenCollateral: address, actualRepayAmount: uint256): (RiskEngineError.Error, uint256)</text>
<text text-anchor="start" x="3735.26" y="-210.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getAllMarkets(): IPToken[]</text>
<text text-anchor="start" x="3735.26" y="-193.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;delegateAllowed(user: address, delegate: address): bool</text>
<text text-anchor="start" x="3735.26" y="-176.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;oracle(): address</text>
<text text-anchor="start" x="3735.26" y="-159.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;collateralFactor(categoryId: uint8, pToken: IPToken): uint256</text>
<text text-anchor="start" x="3735.26" y="-143.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getReserveShares(): (ownerShareMantissa: uint256, configuratorShareMantissa: uint256)</text>
<text text-anchor="start" x="3735.26" y="-126.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;liquidationThreshold(categoryId: uint8, pToken: IPToken): uint256</text>
<text text-anchor="start" x="3735.26" y="-109.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;liquidationIncentive(categoryId: uint8, pToken: address): uint256</text>
<text text-anchor="start" x="3735.26" y="-92.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;closeFactor(pToken: address): uint256</text>
<text text-anchor="start" x="3735.26" y="-75.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;supplyCap(pToken: address): uint256</text>
<text text-anchor="start" x="3735.26" y="-59.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;borrowCap(pToken: address): uint256</text>
<text text-anchor="start" x="3735.26" y="-42.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;emodeMarkets(categoryId: uint8): (collateralTokens: address[], borrowTokens: address[])</text>
<text text-anchor="start" x="3735.26" y="-25.5" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="3735.26" y="-8.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;isDeprecated(pToken: IPToken): bool</text>
</g>
<!-- 34&#45;&gt;9 -->
<g id="edge57" class="edge">
<title>34&#45;&gt;9</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M3727.05,-609.29C3298.98,-686.52 2742.48,-827.37 2302.33,-1077.8 2254.92,-1104.78 2211.8,-1151.05 2185.14,-1183.39"/>
<polygon fill="black" stroke="black" points="2182.31,-1181.33 2178.72,-1191.3 2187.74,-1185.74 2182.31,-1181.33"/>
</g>
<!-- 34&#45;&gt;10 -->
<g id="edge59" class="edge">
<title>34&#45;&gt;10</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M3727.18,-631.9C3222.22,-728.83 2584.64,-884.55 2402.33,-1077.8 2307.05,-1178.8 2316.33,-1557.85 2316.33,-1696.7 2316.33,-1696.7 2316.33,-1696.7 2316.33,-4414.9 2316.33,-4695.72 2388.42,-4787.98 2588.33,-4985.2 2779.74,-5174.03 3085.22,-5277.55 3259.02,-5324.09"/>
<polygon fill="black" stroke="black" points="3258.17,-5327.48 3268.73,-5326.67 3259.96,-5320.72 3258.17,-5327.48"/>
</g>
<!-- 34&#45;&gt;11 -->
<g id="edge64" class="edge">
<title>34&#45;&gt;11</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M3727.03,-616.68C3260.06,-721.91 2668.29,-942.02 2373.33,-1399.6 2301.65,-1510.81 2354.33,-1564.39 2354.33,-1696.7 2354.33,-1696.7 2354.33,-1696.7 2354.33,-2654.5 2354.33,-2930.5 2301.45,-3008.73 2373.33,-3275.2 2563.65,-3980.68 3063.46,-4712.84 3284.07,-5013.26"/>
<polygon fill="black" stroke="black" points="3281.45,-5015.61 3290.19,-5021.59 3287.08,-5011.46 3281.45,-5015.61"/>
</g>
<!-- 34&#45;&gt;16 -->
<g id="edge56" class="edge">
<title>34&#45;&gt;16</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4546.64,-1041.53C4559.54,-1148.75 4558.94,-1260.41 4532.33,-1363.6 4530.03,-1372.53 4527.43,-1381.47 4524.58,-1390.4"/>
<polygon fill="black" stroke="black" points="4521.19,-1389.49 4521.38,-1400.09 4527.84,-1391.69 4521.19,-1389.49"/>
</g>
<!-- 34&#45;&gt;18 -->
<g id="edge54" class="edge">
<title>34&#45;&gt;18</title>
<path fill="none" stroke="black" d="M4259.51,-1041.54C4257.03,-1053.73 4254.63,-1065.83 4252.33,-1077.8 4240.27,-1140.72 4256.16,-1315.07 4214.33,-1363.6 4176.59,-1407.4 4133.35,-1362.12 4089.33,-1399.6 4020.19,-1458.48 3982.89,-1559.1 3964.58,-1626.73"/>
<polygon fill="none" stroke="black" points="3954.33,-1624.43 3957.25,-1656.08 3974.7,-1629.52 3954.33,-1624.43"/>
</g>
<!-- 34&#45;&gt;21 -->
<g id="edge55" class="edge">
<title>34&#45;&gt;21</title>
<path fill="none" stroke="black" d="M4062.55,-1041.36C4039.96,-1076.95 4019.36,-1109.4 4002.1,-1136.58"/>
<polygon fill="none" stroke="black" points="3993.23,-1130.96 3986.02,-1161.91 4010.96,-1142.21 3993.23,-1130.96"/>
</g>
<!-- 34&#45;&gt;24 -->
<g id="edge60" class="edge">
<title>34&#45;&gt;24</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M3726.8,-752.02C3432.7,-890.8 3115.9,-1099.98 2947.33,-1399.6 2882.37,-1515.07 2913.79,-1867.63 2947.33,-1995.8 2949.64,-2004.63 2952.1,-2013.46 2954.7,-2022.29"/>
<polygon fill="black" stroke="black" points="2951.35,-2023.3 2957.57,-2031.88 2958.05,-2021.29 2951.35,-2023.3"/>
</g>
<!-- 34&#45;&gt;25 -->
<g id="edge52" class="edge">
<title>34&#45;&gt;25</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4549.88,-1041.36C4552.81,-1053.6 4555.63,-1065.76 4558.33,-1077.8 4618.9,-1347.81 4626.33,-1419.98 4626.33,-1696.7 4626.33,-1696.7 4626.33,-1696.7 4626.33,-2654.5 4626.33,-3039.88 4619.28,-3472.71 4612.13,-3812.51"/>
<polygon fill="none" stroke="black" points="4601.63,-3812.54 4611.49,-3842.76 4622.63,-3812.99 4601.63,-3812.54"/>
</g>
<!-- 34&#45;&gt;26 -->
<g id="edge61" class="edge">
<title>34&#45;&gt;26</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M5033.64,-1041.5C5193.02,-1226.84 5319.33,-1451.18 5319.33,-1696.7 5319.33,-1696.7 5319.33,-1696.7 5319.33,-3559.9 5319.33,-4195.15 5570.52,-4460.56 5212.33,-4985.2 5163.53,-5056.68 5075.97,-5098.98 5000.27,-5123.45"/>
<polygon fill="black" stroke="black" points="4999.11,-5120.15 4990.63,-5126.49 5001.22,-5126.82 4999.11,-5120.15"/>
</g>
<!-- 34&#45;&gt;27 -->
<g id="edge65" class="edge">
<title>34&#45;&gt;27</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M3727.03,-1027.91C3712.27,-1044.33 3698,-1060.97 3684.33,-1077.8 3553.1,-1239.37 3459.1,-1466.52 3412.21,-1595.94"/>
<polygon fill="black" stroke="black" points="3408.88,-1594.87 3408.78,-1605.47 3415.46,-1597.25 3408.88,-1594.87"/>
</g>
<!-- 34&#45;&gt;29 -->
<g id="edge53" class="edge">
<title>34&#45;&gt;29</title>
<path fill="none" stroke="black" d="M4663.08,-1041.39C4668.31,-1053.6 4673.41,-1065.74 4678.33,-1077.8 4746.62,-1244.89 4804.74,-1445.66 4838.6,-1572.24"/>
<polygon fill="none" stroke="black" points="4828.54,-1575.28 4846.37,-1601.59 4848.84,-1569.91 4828.54,-1575.28"/>
</g>
<!-- 34&#45;&gt;30 -->
<g id="edge58" class="edge">
<title>34&#45;&gt;30</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4870.35,-1041.32C4947.14,-1152.28 5015.16,-1274.11 5057.33,-1399.6 5178.45,-1760.02 5073.77,-2208.64 4994.08,-2459.51"/>
<polygon fill="black" stroke="black" points="4990.73,-2458.5 4991.02,-2469.09 4997.4,-2460.63 4990.73,-2458.5"/>
</g>
<!-- 34&#45;&gt;31 -->
<g id="edge66" class="edge">
<title>34&#45;&gt;31</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4392.33,-1041.36C4392.33,-1050.46 4392.33,-1059.35 4392.33,-1068.01"/>
<polygon fill="black" stroke="black" points="4388.83,-1068.05 4392.33,-1078.05 4395.83,-1068.05 4388.83,-1068.05"/>
</g>
<!-- 34&#45;&gt;32 -->
<g id="edge62" class="edge">
<title>34&#45;&gt;32</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4631.56,-1041.68C4634.73,-1053.76 4637.67,-1065.81 4640.33,-1077.8 4746.29,-1554.56 4572.06,-2798.74 4679.33,-3275.2 4696.04,-3349.39 4733.23,-3427.7 4763.24,-3483.04"/>
<polygon fill="black" stroke="black" points="4760.31,-3484.98 4768.18,-3492.07 4766.45,-3481.62 4760.31,-3484.98"/>
</g>
<!-- 34&#45;&gt;33 -->
<g id="edge63" class="edge">
<title>34&#45;&gt;33</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4962.74,-1041.6C4971.23,-1053.57 4979.44,-1065.65 4987.33,-1077.8 5069.71,-1204.7 5066.76,-1251.64 5098.33,-1399.6 5272.4,-2215.25 5174.06,-3243.8 5145.38,-3498.73"/>
<polygon fill="black" stroke="black" points="5141.88,-3498.55 5144.23,-3508.88 5148.84,-3499.34 5141.88,-3498.55"/>
</g>
</g>
</svg>
