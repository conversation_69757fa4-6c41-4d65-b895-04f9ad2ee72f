{"protocol-info": {"initialGovernor": "0xE80bbcAB9E20fc193Ef768B68d69F363a7f9a2a1", "ownerShareMantissa": "0", "configuratorShareMantissa": "0"}, "market-ws": {"name": "Pike WS", "symbol": "pWS", "baseToken": "0x039e2fB66102314Ce7b64Ce5Ce3E5183bc94aD38", "decimals": 8, "initialExchangeRateMantissa": "1000000000000000000", "reserveFactorMantissa": "150000000000000000", "protocolSeizeShareMantissa": "50000000000000000", "borrowRateMaxMantissa": "1000000000000000000", "collateralFactorMantissa": "800000000000000000", "liquidationThresholdMantissa": "830000000000000000", "liquidationIncentiveMantissa": "1050000000000000000", "closeFactor": "500000000000000000", "supplyCap": "130000000000000000000000", "borrowCap": "110000000000000000000000", "baseRatePerYear": "0", "multiplierPerYear": "0", "firstJumpMultiplierPerYear": "30000000000000000", "secondJumpMultiplierPerYear": "8000000000000000000", "firstKink": "0", "secondKink": "900000000000000000", "mainProvider": "0xE7A9C611Fdc5C47edA3d0a2A0B9a124eb968eBD1", "fallbackProvider": "0x0000000000000000000000000000000000000000", "chainlinkFeed": "0x0000000000000000000000000000000000000000", "pythFeed": "0x0"}, "market-sts": {"name": "Pike stS", "symbol": "pstS", "baseToken": "0xE5DA20F15420aD15DE0fa650600aFc998bbE3955", "decimals": 8, "initialExchangeRateMantissa": "1000000000000000000", "reserveFactorMantissa": "50000000000000000", "protocolSeizeShareMantissa": "60000000000000000", "borrowRateMaxMantissa": "1000000000000000000", "collateralFactorMantissa": "750000000000000000", "liquidationThresholdMantissa": "790000000000000000", "liquidationIncentiveMantissa": "1060000000000000000", "closeFactor": "500000000000000000", "supplyCap": "18000000000000000000000", "borrowCap": "4000000000000000000000", "baseRatePerYear": "0", "multiplierPerYear": "0", "firstJumpMultiplierPerYear": "6111110000000000", "secondJumpMultiplierPerYear": "8500000000000000000", "firstKink": "0", "secondKink": "900000000000000000", "mainProvider": "0xE7A9C611Fdc5C47edA3d0a2A0B9a124eb968eBD1", "fallbackProvider": "0x0000000000000000000000000000000000000000", "chainlinkFeed": "0x0000000000000000000000000000000000000000", "pythFeed": "0x0"}, "market-wos": {"name": "Pike wOS", "symbol": "pwOS", "baseToken": "0x9F0dF7799f6FDAd409300080cfF680f5A23df4b1", "decimals": 8, "initialExchangeRateMantissa": "1000000000000000000", "reserveFactorMantissa": "450000000000000000", "protocolSeizeShareMantissa": "75000000000000000", "borrowRateMaxMantissa": "1000000000000000000", "collateralFactorMantissa": "725000000000000000", "liquidationThresholdMantissa": "750000000000000000", "liquidationIncentiveMantissa": "1075000000000000000", "closeFactor": "500000000000000000", "supplyCap": "100000000000000000000000", "borrowCap": "20000000000000000000000", "baseRatePerYear": "0", "multiplierPerYear": "0", "firstJumpMultiplierPerYear": "260000000000000000", "secondJumpMultiplierPerYear": "4280000000000000000", "firstKink": "0", "secondKink": "300000000000000000", "mainProvider": "0xE7A9C611Fdc5C47edA3d0a2A0B9a124eb968eBD1", "fallbackProvider": "0x0000000000000000000000000000000000000000", "chainlinkFeed": "0x0000000000000000000000000000000000000000", "pythFeed": "0x0"}, "market-ws_wsts": {"name": "Pike WS/stS", "symbol": "pWSstS", "baseToken": "0x9Afd6A2923a0F9369d07E9392cF1B4148391b419", "decimals": 8, "initialExchangeRateMantissa": "1000000000000000000", "reserveFactorMantissa": "50000000000000000", "protocolSeizeShareMantissa": "50000000000000000", "borrowRateMaxMantissa": "1000000000000000000", "collateralFactorMantissa": "750000000000000000", "liquidationThresholdMantissa": "800000000000000000", "liquidationIncentiveMantissa": "1050000000000000000", "closeFactor": "500000000000000000", "supplyCap": "100000000000000000000000", "borrowCap": "20000000000000000000000", "baseRatePerYear": "0", "multiplierPerYear": "0", "firstJumpMultiplierPerYear": "30000000000000000", "secondJumpMultiplierPerYear": "8000000000000000000", "firstKink": "0", "secondKink": "900000000000000000", "mainProvider": "0xE7A9C611Fdc5C47edA3d0a2A0B9a124eb968eBD1", "fallbackProvider": "0x0000000000000000000000000000000000000000", "chainlinkFeed": "0x0000000000000000000000000000000000000000", "pythFeed": "0x0"}, "market-ws_wosh": {"name": "Pike WS/wOS", "symbol": "pWSwOS", "baseToken": "0xc71BBbf481Fb6ddBeE3389a0E4676eCcCdb08E5c", "decimals": 8, "initialExchangeRateMantissa": "1000000000000000000", "reserveFactorMantissa": "50000000000000000", "protocolSeizeShareMantissa": "50000000000000000", "borrowRateMaxMantissa": "1000000000000000000", "collateralFactorMantissa": "750000000000000000", "liquidationThresholdMantissa": "800000000000000000", "liquidationIncentiveMantissa": "1050000000000000000", "closeFactor": "500000000000000000", "supplyCap": "100000000000000000000000", "borrowCap": "20000000000000000000000", "baseRatePerYear": "0", "multiplierPerYear": "0", "firstJumpMultiplierPerYear": "30000000000000000", "secondJumpMultiplierPerYear": "8000000000000000000", "firstKink": "0", "secondKink": "900000000000000000", "mainProvider": "0xE7A9C611Fdc5C47edA3d0a2A0B9a124eb968eBD1", "fallbackProvider": "0x0000000000000000000000000000000000000000", "chainlinkFeed": "0x0000000000000000000000000000000000000000", "pythFeed": "0x0"}, "emode-1": {"categoryId": 1, "ptokens": ["0x639D9dD66EB95655E5eB154CC2498fF9A78f8EB5", "0x9F1864cFBCDf6d10A403aE79b820Fa0153919a5E", "0x9ed3eab6Df6b6b07601717eC336DfFD97A8fdE98", "0x016E759c1144499af34Db367C34A8D2c5347Aa43", "0xD6a631850CB6395dBb07FaAd2BDecFD88eF927e0"], "collateralPermissions": [false, false, false, true, true], "borrowPermissions": [true, true, true, false, false], "riskConfig": {"collateralFactorMantissa": "900000000000000000", "liquidationThresholdMantissa": "930000000000000000", "liquidationIncentiveMantissa": "1020000000000000000"}}}