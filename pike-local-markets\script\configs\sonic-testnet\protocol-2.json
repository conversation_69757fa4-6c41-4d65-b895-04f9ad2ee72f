{"protocol-info": {"initialGovernor": "0x92Bc7D2305EC97E138a5c98F6f5FD69703fe0589", "ownerShareMantissa": "0", "configuratorShareMantissa": "0"}, "market-ws": {"name": "Pike WS", "symbol": "pWS", "baseToken": "0x98e6a95a4B225b60456FC5dEDde3E4b8B7655C6B", "decimals": 8, "initialExchangeRateMantissa": "1000000000000000000", "reserveFactorMantissa": "10000000000000000", "protocolSeizeShareMantissa": "20000000000000000", "borrowRateMaxMantissa": "1000000000000000000", "collateralFactorMantissa": "725000000000000000", "liquidationThresholdMantissa": "825000000000000000", "liquidationIncentiveMantissa": "1020000000000000000", "closeFactor": "500000000000000000", "supplyCap": "1000000000000000000", "borrowCap": "1000000000000000000", "baseRatePerYear": "0", "multiplierPerYear": "0", "firstJumpMultiplierPerYear": "61111110000000000", "secondJumpMultiplierPerYear": "6000000000000000000", "firstKink": "50000000000000000", "secondKink": "950000000000000000", "mainProvider": "0x09E49f1F2536ABe40A327155f5168eAD82df7F37", "fallbackProvider": "0x0000000000000000000000000000000000000000"}, "market-sts": {"name": "Pike stS", "symbol": "pSTS", "baseToken": "0x6A074e2158e9C0f7A2e738f4919DC42316d153B0", "decimals": 8, "initialExchangeRateMantissa": "1000000000000000000", "reserveFactorMantissa": "10000000000000000", "protocolSeizeShareMantissa": "20000000000000000", "borrowRateMaxMantissa": "1000000000000000000", "collateralFactorMantissa": "725000000000000000", "liquidationThresholdMantissa": "825000000000000000", "liquidationIncentiveMantissa": "1020000000000000000", "closeFactor": "500000000000000000", "supplyCap": "100000000000000000000", "borrowCap": "100000000000000000000", "baseRatePerYear": "0", "multiplierPerYear": "0", "firstJumpMultiplierPerYear": "61111110000000000", "secondJumpMultiplierPerYear": "6000000000000000000", "firstKink": "50000000000000000", "secondKink": "950000000000000000", "mainProvider": "0x09E49f1F2536ABe40A327155f5168eAD82df7F37", "fallbackProvider": "0x0000000000000000000000000000000000000000"}, "market-os": {"name": "Pike OS", "symbol": "pOS", "baseToken": "0xa22a772520aaaaCEaD463593871C34e6A4422c83", "decimals": 8, "initialExchangeRateMantissa": "1000000000000000000", "reserveFactorMantissa": "10000000000000000", "protocolSeizeShareMantissa": "20000000000000000", "borrowRateMaxMantissa": "1000000000000000000", "collateralFactorMantissa": "750000000000000000", "liquidationThresholdMantissa": "850000000000000000", "liquidationIncentiveMantissa": "1020000000000000000", "closeFactor": "500000000000000000", "supplyCap": "100000000000000000000", "borrowCap": "100000000000000000000", "baseRatePerYear": "0", "multiplierPerYear": "0", "firstJumpMultiplierPerYear": "61111110000000000", "secondJumpMultiplierPerYear": "6000000000000000000", "firstKink": "50000000000000000", "secondKink": "950000000000000000", "mainProvider": "0x09E49f1F2536ABe40A327155f5168eAD82df7F37", "fallbackProvider": "0x0000000000000000000000000000000000000000"}, "market-ws_sts": {"name": "Pike WS/stS", "symbol": "pWSSTS", "baseToken": "0x8080BC8D172A206C1dBB6285fa87697550fcEe37", "decimals": 8, "initialExchangeRateMantissa": "1000000000000000000", "reserveFactorMantissa": "50000000000000000", "protocolSeizeShareMantissa": "50000000000000000", "borrowRateMaxMantissa": "1000000000000000000", "collateralFactorMantissa": "750000000000000000", "liquidationThresholdMantissa": "800000000000000000", "liquidationIncentiveMantissa": "1050000000000000000", "closeFactor": "500000000000000000", "supplyCap": "100000000000000000000000", "borrowCap": "20000000000000000000000", "baseRatePerYear": "0", "multiplierPerYear": "0", "firstJumpMultiplierPerYear": "30000000000000000", "secondJumpMultiplierPerYear": "8000000000000000000", "firstKink": "0", "secondKink": "900000000000000000", "mainProvider": "0x09E49f1F2536ABe40A327155f5168eAD82df7F37", "fallbackProvider": "0x0000000000000000000000000000000000000000", "chainlinkFeed": "0x0000000000000000000000000000000000000000", "pythFeed": "0x0000000000000000000000000000000000000000"}, "market-ws_os": {"name": "Pike WS/OS", "symbol": "pWSOS", "baseToken": "0x61CC97d5B632b227B00aC5d976fF1422Da4CF8F7", "decimals": 8, "initialExchangeRateMantissa": "1000000000000000000", "reserveFactorMantissa": "50000000000000000", "protocolSeizeShareMantissa": "50000000000000000", "borrowRateMaxMantissa": "1000000000000000000", "collateralFactorMantissa": "750000000000000000", "liquidationThresholdMantissa": "800000000000000000", "liquidationIncentiveMantissa": "1050000000000000000", "closeFactor": "500000000000000000", "supplyCap": "100000000000000000000000", "borrowCap": "20000000000000000000000", "baseRatePerYear": "0", "multiplierPerYear": "0", "firstJumpMultiplierPerYear": "30000000000000000", "secondJumpMultiplierPerYear": "8000000000000000000", "firstKink": "0", "secondKink": "900000000000000000", "mainProvider": "0x09E49f1F2536ABe40A327155f5168eAD82df7F37", "fallbackProvider": "0x0000000000000000000000000000000000000000", "chainlinkFeed": "0x0000000000000000000000000000000000000000", "pythFeed": "0x0000000000000000000000000000000000000000"}, "emode-1": {"categoryId": 1, "ptokens": ["0x363A541E26d2721252df012b2e86E823C143a30d", "0x03E33b9A27E595D5f27917B4333ed0C057B2328d", "0x85903894C7907990a8717E698C696feD521544E2", "0x2A89503fC918FA0c4AC78D9864B619b29c383f7b", "0xD04c03B0cb090F9eF4695bF5AD32416F040eB96b"], "collateralPermissions": [false, false, false, true, true], "borrowPermissions": [true, true, true, false, false], "riskConfig": {"collateralFactorMantissa": "900000000000000000", "liquidationThresholdMantissa": "930000000000000000", "liquidationIncentiveMantissa": "1020000000000000000"}}}