name: "CI"

env:
  FOUNDRY_PROFILE: "ci"

on:
  workflow_dispatch:
  pull_request:
  push:
    branches:
      - "main"

jobs:
  lint:
    runs-on: "ubuntu-latest"
    steps:
      - name: "Check out the repo"
        uses: "actions/checkout@v4"

      - uses: actions/setup-node@v4
        with:
          node-version: 18
          cache: "yarn"

      - name: "Install the Node.js dependencies"
        run: "yarn install"

      - name: "Install Foundry"
        uses: "foundry-rs/foundry-toolchain@v1"

      - name: "Lint the code"
        run: "yarn run lint"

      - name: "Add lint summary"
        run: |
          echo "## Lint result" >> $GITHUB_STEP_SUMMARY
          echo "✅ Passed" >> $GITHUB_STEP_SUMMARY

  build:
    runs-on: "ubuntu-latest"
    steps:
      - name: "Check out the repo"
        uses: "actions/checkout@v4"

      - uses: actions/setup-node@v4
        with:
          node-version: 18
          cache: "yarn"

      - name: "Install the Node.js dependencies"
        run: "yarn install"

      - name: "Install Foundry"
        uses: "foundry-rs/foundry-toolchain@v1"

      - name: "Build the contracts and print their size"
        run: "forge build --sizes"

      - name: "Add build summary"
        run: |
          echo "## Build result" >> $GITHUB_STEP_SUMMARY
          echo "✅ Passed" >> $GITHUB_STEP_SUMMARY

  test:
    needs: ["lint", "build"]
    runs-on: "ubuntu-latest"
    steps:
      - name: "Check out the repo"
        uses: "actions/checkout@v4"

      - uses: actions/setup-node@v4
        with:
          node-version: 18
          cache: "yarn"

      - name: "Install the Node.js dependencies"
        run: "yarn install"

      - name: "Install Foundry"
        uses: "foundry-rs/foundry-toolchain@v1"

      - name: "Show the Foundry config"
        run: "forge config"

      - name: "Generate a fuzz seed that changes weekly to avoid burning through RPC allowance"
        run: >
          echo "FOUNDRY_FUZZ_SEED=$(
            echo $(($EPOCHSECONDS - $EPOCHSECONDS % 604800))
          )" >> $GITHUB_ENV

      - name: "Run the tests"
        run: "forge test"

      - name: "Add test summary"
        run: |
          echo "## Tests result" >> $GITHUB_STEP_SUMMARY
          echo "✅ Passed" >> $GITHUB_STEP_SUMMARY
