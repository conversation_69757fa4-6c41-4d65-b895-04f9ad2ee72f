# Tapio Protocol

Tapio is a liquidity aggregator that acts as the middle layer between self-pegging assets and downstream DeFi
applications. Liquidity is a cornerstone of DeFi and represents the most significant use case for self-pegging assets
when paired with complementary tokens. However, once these assets are utilized in liquidity pools and the resulting LP
token is obtained, the journey often stalls, leaving limited options for further use. Additionally, the ecosystem faces
heavy fragmentation of self-pegging asset liquidity, with individual protocols employing distinct reward mechanisms,
token models, and architectures—resulting in an increasingly fragmented and inefficient landscape.

Introducing the Tapio LP Token—the first utility-focused LP token designed for the self-pegging asset ecosystem. Serving
as both a liquidity aggregator and an arbitrage hub, it fundamentally operates as a rebasing LP token that represents
diverse stable pools within Tapio Finance. These pools include pairings between self-pegging assets and their
complements, such as:

- stETH-ETH
- USDT-USDC
- rETH-wstETH

Unlike traditional liquidity pools in DEXs, the Tapio LP Token you receive after depositing assets is pegged to the
underlying assets and can be seamlessly used within DeFi or as a medium of exchange, just like any other asset. The
Tapio LP Token also accrues underlying token rewards and fees generated by the pools—such as swaps, redemptions, and
more—delivering an attractive real yield APR even before engaging in further DeFi activities.

For added flexibility, the Tapio LP Token can be wrapped into a wrapped LP token, adopting an "interest rate" model that
simplifies integration and cross-chain usage.

As a synthetic asset protocol, Tapio Finance dynamically adjusts the pricing curves of self-pegging asset pools,
enabling efficient swaps and arbitrage opportunities. This ensures the stability of tapETH while unlocking excellent use
cases for Tapio's liquidity pools.

## Overview

In Tapio, users can:

- Create new AMM pools using Factory.
- Mint, swap and redeem tokens in pools.
- Wrap and unwrap LP tokens.

The protocol is built using modular, upgradeable components and is based on CurveFi's StableSwap algorithm. This design
ensuring the system can adapt and scale with evolving protocol requirements.

## Contracts

Tapio uses several core contracts to facilitate management of pools and it's functionality:

### SelfPeggingAssetFactory

The SelfPeggingAssetFactory contract automates the deployment of new pools, simplifying their creation and management.
All pools deployed through the Factory are governed by Pike's governance system.

### SelfPeggingAsset

The SelfPeggingAsset conttract represents a AMM pool. It allows users to swap, mint and redeem tokens of the pool. It
implements the StableSwap algorithm.

### LPToken

The LPToken is an ERC20 rebase token issued by StableSwap pools to liquidity providers.

LPToken balances are dynamic and reflect the holder's share of the total LPToken supply managed by the protocol. Since
account shares are not normalized, the contract tracks the total sum of all shares to compute each account's token
balance using the formula:

```
shares[account] * _totalSupply / _totalShares
```

Here, \_totalSupply represents the total amount of LPToken managed by the protocol.

### WLPToken

The WLPToken is an ERC4626 standard token that represents an account's share of the total LPToken supply. Unlike
LPToken, which dynamically updates balances based on staking rewards and swap fees, WLPToken balances only change during
transfers.

Designed as a "power user" token, WLPToken caters to DeFi protocols that do not support rebasable tokens. The contract
serves as a trustless wrapper, accepting LPToken and minting WLPToken in return. When users choose to unwrap, the
contract burns their WLPToken and returns the corresponding locked LPToken.

## Usage

This is a list of the most frequently needed commands.

### Build

Build the contracts:

```sh
$ forge build
```

### Clean

Delete the build artifacts and cache directories:

```sh
$ forge clean
```

### Compile

Compile the contracts:

```sh
$ forge build
```

### Coverage

Get a test coverage report:

```sh
$ forge coverage
```

### Deploy to Testnet

Deploy to Base Testnet:

```sh
$ forge script ./script/Testnet.s.sol -vvv --rpc-url basesepolia --broadcast
```

Before deploying make sure you configure the neccessary variables in `.env` file. To just test the scripts with just a
dry run remove the `--broadcast` flag.

### Verifying Contracts on Testnet Explorer

Here is an example on how to verify a contract on base sepolia:

```sh
$ forge verify-contract <contract-address> <contract-name> --watch --etherscan-api-key <basescan-api-key>  --chain-id 84532 --constructor-args <encoded-constructor-args>
```

You can find the contract name and constructor args in the broadcast directory. To encode the constructor args you can
use: https://abi.hashex.org/

### Format

Format the contracts:

```sh
$ forge fmt
```

### Gas Usage

Get a gas report:

```sh
$ forge test --gas-report
```

### Lint

Lint the contracts:

```sh
$ yarn run lint
```

### Test

Run the tests:

```sh
$ forge test
```

Generate test coverage and output result to the terminal:

```sh
$ yarn run test:coverage
```

Generate test coverage with lcov report (you'll have to open the `./coverage/index.html` file in your browser, to do so
simply copy paste the path):

```sh
$ yarn run test:coverage:report
```

## License

This project is licensed under MIT.
