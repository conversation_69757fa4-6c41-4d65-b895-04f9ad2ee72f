{"name": "tapio", "description": "Tapio", "version": "1.0.0", "author": {"name": "NUTS Finance", "email": "<EMAIL>", "url": "https://nuts.finance"}, "dependencies": {"@chainlink/contracts": "^1.2.0", "@openzeppelin/contracts": "^5.1.0", "@openzeppelin/contracts-upgradeable": "^5.1.0"}, "devDependencies": {"forge-std": "github:foundry-rs/forge-std#v1.8.1", "prettier": "^3.0.0", "solhint": "^3.6.2", "solhint-plugin-prettier": "0.1.0"}, "private": true, "scripts": {"clean": "rm -rf cache out", "build": "forge build", "lint": "yarn run lint:sol && yarn run prettier:check", "lint:fix": "forge fmt && yarn solhint \"{script,src,test}/**/*.sol\" --fix", "lint:sol": "forge fmt --check && yarn solhint \"{script,src,test}/**/*.sol\"", "prettier:check": "prettier --check \"**/*.{json,md,yml}\" --ignore-path \".prettierignore\"", "prettier:write": "prettier --write \"**/*.{json,md,yml}\" --ignore-path \".prettierignore\"", "test": "forge test", "test:coverage:report": "forge coverage --ir-minimum --no-match-coverage \"(script|test|node_modules|src/mock/|src/misc/)\" --report lcov && genhtml lcov.info --branch-coverage --output-dir coverage --ignore-errors inconsistent,category"}}